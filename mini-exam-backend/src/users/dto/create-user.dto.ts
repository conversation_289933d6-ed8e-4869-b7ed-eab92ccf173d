import { IsString, IsEmail, IsOptional, IsBoolean, Min<PERSON><PERSON>th } from 'class-validator';

export class CreateUserDto {
  @IsString()
  @MinLength(3)
  username: string;

  @IsString()
  @MinLength(6)
  password: string;

  @IsString()
  @IsOptional()
  @MinLength(2)
  name?: string;

  @IsString()
  @IsOptional()
  @MinLength(2)
  lastName?: string;

  @IsBoolean()
  @IsOptional()
  isAdmin?: boolean;
}
