import {
  Injectable,
  ConflictException,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User, UserRole } from '../entities/user.entity';
import { JwtPayload } from './jwt.strategy';

export interface LoginResponse {
  access_token: string;
  user: {
    id: number;
    username: string;
    role: UserRole;
    name?: string;
    lastName?: string;
  };
}

export interface RegisterDto {
  username: string;
  password: string;
  role?: UserRole;
  name?: string;
  lastName?: string;
}

@Injectable()
export class AuthService {
  constructor(
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private jwtService: JwtService,
  ) {}

  async validateUser(username: string, password: string): Promise<User | null> {
    const user = await this.userRepository.findOne({
      where: { username },
    });

    if (user && (await bcrypt.compare(password, user.password))) {
      return user;
    }

    return null;
  }

  async login(user: User): Promise<LoginResponse> {
    const payload: JwtPayload = {
      sub: user.id,
      username: user.username,
      role: user.role,
    };

    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user.id,
        username: user.username,
        role: user.role,
        name: user.name,
        lastName: user.lastName,
      },
    };
  }

  async register(registerDto: RegisterDto): Promise<LoginResponse> {
    const existingUser = await this.userRepository.findOne({
      where: { username: registerDto.username },
    });

    if (existingUser) {
      throw new ConflictException('Username already exists');
    }

    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    const user = this.userRepository.create({
      username: registerDto.username,
      password: hashedPassword,
      role: registerDto.role || UserRole.USER,
      name: registerDto.name,
      lastName: registerDto.lastName,
    });

    const savedUser = await this.userRepository.save(user);
    return this.login(savedUser);
  }
}
