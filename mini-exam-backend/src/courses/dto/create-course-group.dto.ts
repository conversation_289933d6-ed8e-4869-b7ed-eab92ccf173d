import { <PERSON><PERSON><PERSON>, <PERSON>Not<PERSON>mpty, <PERSON><PERSON><PERSON>ber, IsOptional } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class CreateCourseGroupDto {
  @ApiProperty({ description: 'Name of the course group' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ description: 'Positive coefficient for the course group', default: 1, required: false })
  @IsNumber()
  @IsOptional()
  positiveCoefficient?: number;
}
