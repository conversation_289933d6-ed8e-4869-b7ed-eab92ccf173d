import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Course } from '../entities/course.entity';
import { CourseGroup } from '../entities/course-group.entity';
import { CreateCourseDto } from './dto/create-course.dto';
import { UpdateCourseDto } from './dto/update-course.dto';

@Injectable()
export class CoursesService {
  constructor(
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
    @InjectRepository(CourseGroup)
    private courseGroupRepository: Repository<CourseGroup>,
  ) {}

  async create(createCourseDto: CreateCourseDto): Promise<Course> {
    // Verify that the course group exists
    const courseGroup = await this.courseGroupRepository.findOne({
      where: { id: createCourseDto.courseGroupId },
    });

    if (!courseGroup) {
      throw new BadRequestException(`Course group with ID ${createCourseDto.courseGroupId} not found`);
    }

    const course = this.courseRepository.create(createCourseDto);
    return this.courseRepository.save(course);
  }

  async findAll(): Promise<Course[]> {
    return this.courseRepository.find({
      relations: ['courseGroup'],
    });
  }

  async findOne(id: number): Promise<Course> {
    const course = await this.courseRepository.findOne({
      where: { id },
      relations: ['courseGroup'],
    });

    if (!course) {
      throw new NotFoundException(`Course with ID ${id} not found`);
    }

    return course;
  }

  async findByCourseGroup(courseGroupId: number): Promise<Course[]> {
    return this.courseRepository.find({
      where: { courseGroupId },
      relations: ['courseGroup'],
    });
  }

  async update(id: number, updateCourseDto: UpdateCourseDto): Promise<Course> {
    const course = await this.findOne(id);

    // If courseGroupId is being updated, verify it exists
    if (updateCourseDto.courseGroupId !== undefined) {
      const courseGroup = await this.courseGroupRepository.findOne({
        where: { id: updateCourseDto.courseGroupId },
      });

      if (!courseGroup) {
        throw new BadRequestException(`Course group with ID ${updateCourseDto.courseGroupId} not found`);
      }
    }

    Object.assign(course, updateCourseDto);
    return this.courseRepository.save(course);
  }

  async remove(id: number): Promise<void> {
    const course = await this.findOne(id);
    await this.courseRepository.remove(course);
  }
}
