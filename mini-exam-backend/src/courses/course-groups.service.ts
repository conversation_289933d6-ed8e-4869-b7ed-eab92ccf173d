import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { CourseGroup } from '../entities/course-group.entity';
import { CreateCourseGroupDto } from './dto/create-course-group.dto';
import { UpdateCourseGroupDto } from './dto/update-course-group.dto';

@Injectable()
export class CourseGroupsService {
  constructor(
    @InjectRepository(CourseGroup)
    private courseGroupRepository: Repository<CourseGroup>,
  ) {}

  async create(createCourseGroupDto: CreateCourseGroupDto): Promise<CourseGroup> {
    const courseGroup = this.courseGroupRepository.create(createCourseGroupDto);
    return this.courseGroupRepository.save(courseGroup);
  }

  async findAll(): Promise<CourseGroup[]> {
    return this.courseGroupRepository.find({
      relations: ['courses'],
    });
  }

  async findOne(id: number): Promise<CourseGroup> {
    const courseGroup = await this.courseGroupRepository.findOne({
      where: { id },
      relations: ['courses'],
    });

    if (!courseGroup) {
      throw new NotFoundException(`Course group with ID ${id} not found`);
    }

    return courseGroup;
  }

  async update(id: number, updateCourseGroupDto: UpdateCourseGroupDto): Promise<CourseGroup> {
    const courseGroup = await this.findOne(id);
    Object.assign(courseGroup, updateCourseGroupDto);
    return this.courseGroupRepository.save(courseGroup);
  }

  async remove(id: number): Promise<void> {
    const courseGroup = await this.findOne(id);
    await this.courseGroupRepository.remove(courseGroup);
  }
}
