import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CoursesService } from './courses.service';
import { CourseGroupsService } from './course-groups.service';
import { CoursesController } from './courses.controller';
import { CourseGroupsController } from './course-groups.controller';
import { Course } from '../entities/course.entity';
import { CourseGroup } from '../entities/course-group.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Course, CourseGroup])],
  controllers: [CoursesController, CourseGroupsController],
  providers: [CoursesService, CourseGroupsService],
  exports: [CoursesService, CourseGroupsService],
})
export class CoursesModule {}
