import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { CourseGroupsService } from './course-groups.service';
import { CreateCourseGroupDto } from './dto/create-course-group.dto';
import { UpdateCourseGroupDto } from './dto/update-course-group.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';

@ApiTags('Course Groups')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('course-groups')
export class CourseGroupsController {
  constructor(private readonly courseGroupsService: CourseGroupsService) {}

  @ApiOperation({ summary: 'Create a new course group' })
  @ApiResponse({ status: 201, description: 'Course group created successfully' })
  @Post()
  create(@Body() createCourseGroupDto: CreateCourseGroupDto) {
    return this.courseGroupsService.create(createCourseGroupDto);
  }

  @ApiOperation({ summary: 'Get all course groups' })
  @ApiResponse({ status: 200, description: 'List of course groups' })
  @Get()
  findAll() {
    return this.courseGroupsService.findAll();
  }

  @ApiOperation({ summary: 'Get a course group by ID' })
  @ApiResponse({ status: 200, description: 'Course group found' })
  @ApiResponse({ status: 404, description: 'Course group not found' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.courseGroupsService.findOne(+id);
  }

  @ApiOperation({ summary: 'Update a course group' })
  @ApiResponse({ status: 200, description: 'Course group updated successfully' })
  @ApiResponse({ status: 404, description: 'Course group not found' })
  @Patch(':id')
  update(@Param('id') id: string, @Body() updateCourseGroupDto: UpdateCourseGroupDto) {
    return this.courseGroupsService.update(+id, updateCourseGroupDto);
  }

  @ApiOperation({ summary: 'Delete a course group' })
  @ApiResponse({ status: 200, description: 'Course group deleted successfully' })
  @ApiResponse({ status: 404, description: 'Course group not found' })
  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.courseGroupsService.remove(+id);
  }
}
