import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsString, IsNotEmpty, IsNumber } from 'class-validator';
import { Course } from './course.entity';

@Entity('course_groups')
export class CourseGroup {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @IsString()
  @IsNotEmpty()
  name: string;

  @Column('float', { name: 'positive_coefficient', default: 1 })
  @IsNumber()
  positiveCoefficient: number;

  @OneToMany(() => Course, course => course.courseGroup)
  courses: Course[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
