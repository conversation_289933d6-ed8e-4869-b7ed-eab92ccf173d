import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsString, IsNotEmpty, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { Session } from './session.entity';

export class ExamCourse {
  @IsNotEmpty()
  courseId: number;

  @IsNotEmpty()
  questionCount: number;
}

@Entity('exams')
export class Exam {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @IsString()
  @IsNotEmpty()
  name: string;

  @Column({ type: 'jsonb' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExamCourse)
  courses: ExamCourse[];

  @OneToMany(() => Session, session => session.exam)
  sessions: Session[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
