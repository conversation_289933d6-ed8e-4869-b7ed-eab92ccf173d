import { <PERSON><PERSON><PERSON>, PrimaryGenerated<PERSON><PERSON>umn, Column, ManyToOne, <PERSON><PERSON><PERSON><PERSON><PERSON>n, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsString, IsNotEmpty, IsInt, Min, Max } from 'class-validator';
import { CourseGroup } from './course-group.entity';

@Entity('courses')
export class Course {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  @IsString()
  @IsNotEmpty()
  name: string;

  @Column({ name: 'course_group_id' })
  courseGroupId: number;

  @ManyToOne(() => CourseGroup, courseGroup => courseGroup.courses)
  @JoinColumn({ name: 'course_group_id' })
  courseGroup: CourseGroup;

  @Column({ name: 'positive_coefficient' })
  @IsInt()
  @Min(1)
  @Max(99)
  positiveCoefficient: number;

  @Column({ name: 'negative_coefficient' })
  @IsInt()
  @Min(1)
  @Max(99)
  negativeCoefficient: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
