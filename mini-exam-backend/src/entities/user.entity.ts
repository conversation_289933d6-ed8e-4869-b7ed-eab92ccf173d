import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { IsString, IsEnum, MinLength } from 'class-validator';
import { Session } from './session.entity';

export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  @IsString()
  @MinLength(3)
  username: string;

  @Column()
  @IsString()
  @MinLength(6)
  password: string;

  @Column({ nullable: true })
  @IsString()
  @MinLength(2)
  name: string;

  @Column({ nullable: true })
  @IsString()
  @MinLength(2)
  lastName: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.USER,
  })
  @IsEnum(UserRole)
  role: UserRole;

  @OneToMany(() => Session, session => session.user)
  sessions: Session[];

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}
