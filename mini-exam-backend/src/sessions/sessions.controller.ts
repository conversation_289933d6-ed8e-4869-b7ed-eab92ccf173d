import { <PERSON>, Get, Post, Body, Param, UseGuards, Request, Query, Patch } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { SessionsService } from './sessions.service';
import { CreateSessionDto } from './dto/create-session.dto';
import { JwtAuthGuard } from '../auth/jwt-auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { UserRole } from '../entities/user.entity';

@ApiTags('Sessions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('sessions')
export class SessionsController {
  constructor(private readonly sessionsService: SessionsService) {}

  @ApiOperation({ summary: 'Create a new session (submit exam answers)' })
  @ApiResponse({ status: 201, description: 'Session created successfully with calculated scores' })
  @ApiResponse({ status: 400, description: 'Invalid answers or exam data' })
  @ApiResponse({ status: 404, description: 'Exam not found' })
  @Post()
  create(@Body() createSessionDto: CreateSessionDto, @Request() req) {
    return this.sessionsService.create(createSessionDto, req.user.id);
  }

  @ApiOperation({ summary: 'Create a session for a specific user (admin only)' })
  @ApiResponse({ status: 201, description: 'Session created successfully for the specified user' })
  @ApiResponse({ status: 400, description: 'Invalid answers or exam data' })
  @ApiResponse({ status: 404, description: 'Exam or user not found' })
  @Roles(UserRole.ADMIN)
  @Post('for-user/:userId')
  createForUser(@Param('userId') userId: string, @Body() createSessionDto: CreateSessionDto) {
    return this.sessionsService.create(createSessionDto, +userId);
  }

  @ApiOperation({ summary: 'Get all sessions (admin only)' })
  @ApiResponse({ status: 200, description: 'List of all sessions' })
  @Roles(UserRole.ADMIN)
  @Get()
  findAll() {
    return this.sessionsService.findAll();
  }

  @ApiOperation({ summary: 'Get current user sessions' })
  @ApiResponse({ status: 200, description: 'List of user sessions' })
  @Get('my-sessions')
  findMySessions(@Request() req) {
    return this.sessionsService.findByUser(req.user.id);
  }

  @ApiOperation({ summary: 'Get sessions by exam ID' })
  @ApiResponse({ status: 200, description: 'List of sessions for the exam' })
  @Get('by-exam/:examId')
  findByExam(@Param('examId') examId: string) {
    return this.sessionsService.findByExam(+examId);
  }

  @ApiOperation({ summary: 'Get exam statistics' })
  @ApiResponse({ status: 200, description: 'Exam statistics including score distribution' })
  @Get('statistics/:examId')
  getExamStatistics(@Param('examId') examId: string) {
    return this.sessionsService.getExamStatistics(+examId);
  }

  @ApiOperation({ summary: 'Get a session by ID' })
  @ApiResponse({ status: 200, description: 'Session found' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.sessionsService.findOne(+id);
  }

  @ApiOperation({ summary: 'Recalculate session scores (admin only)' })
  @ApiResponse({ status: 200, description: 'Session scores recalculated successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  @Roles(UserRole.ADMIN)
  @Patch(':id/recalculate')
  recalculateSession(@Param('id') id: string) {
    return this.sessionsService.recalculateSession(+id);
  }
}
