import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotFoundException } from '@nestjs/common';
import { SessionsService } from './sessions.service';
import { Session } from '../entities/session.entity';
import { Exam } from '../entities/exam.entity';
import { User } from '../entities/user.entity';
import { ScoringService } from '../scoring/scoring.service';

describe('SessionsService', () => {
  let service: SessionsService;
  let sessionRepository: Repository<Session>;
  let examRepository: Repository<Exam>;
  let userRepository: Repository<User>;
  let scoringService: ScoringService;

  const mockSession = {
    id: 1,
    examId: 1,
    userId: 1,
    totalScore: 75.5,
    scoreDetails: {
      answers: [
        { courseId: 1, correctAnswers: 8, wrongAnswers: 2, unanswered: 0 },
        { courseId: 2, correctAnswers: 7, wrongAnswers: 3, unanswered: 0 },
      ],
      courseScores: [
        { courseId: 1, courseName: 'ریاضی', percentage: 80 },
        { courseId: 2, courseName: 'فیزیک', percentage: 70 },
      ],
      courseGroupScores: [
        { courseGroupId: 1, courseGroupName: 'علوم پایه', averageScore: 75, weightedScore: 75 },
      ],
    },
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockExam = {
    id: 1,
    name: 'آزمون نمونه',
    courses: [
      { courseId: 1, questionCount: 10 },
      { courseId: 2, questionCount: 10 },
    ],
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  const mockRecalculatedScores = {
    scoreDetails: {
      answers: mockSession.scoreDetails.answers,
      courseScores: [
        { courseId: 1, courseName: 'ریاضی', percentage: 85 },
        { courseId: 2, courseName: 'فیزیک', percentage: 75 },
      ],
      courseGroupScores: [
        { courseGroupId: 1, courseGroupName: 'علوم پایه', averageScore: 80, weightedScore: 80 },
      ],
    },
    totalScore: 80,
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SessionsService,
        {
          provide: getRepositoryToken(Session),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(Exam),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: ScoringService,
          useValue: {
            calculateScores: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<SessionsService>(SessionsService);
    sessionRepository = module.get<Repository<Session>>(getRepositoryToken(Session));
    examRepository = module.get<Repository<Exam>>(getRepositoryToken(Exam));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    scoringService = module.get<ScoringService>(ScoringService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('recalculateSession', () => {
    it('should successfully recalculate session scores', async () => {
      // Arrange
      jest.spyOn(sessionRepository, 'findOne').mockResolvedValue(mockSession as Session);
      jest.spyOn(examRepository, 'findOne').mockResolvedValue(mockExam as Exam);
      jest.spyOn(scoringService, 'calculateScores').mockResolvedValue(mockRecalculatedScores);
      jest.spyOn(sessionRepository, 'save').mockResolvedValue({
        ...mockSession,
        totalScore: mockRecalculatedScores.totalScore,
        scoreDetails: mockRecalculatedScores.scoreDetails,
      } as Session);

      // Act
      const result = await service.recalculateSession(1);

      // Assert
      expect(sessionRepository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
        relations: ['exam', 'user'],
      });
      expect(examRepository.findOne).toHaveBeenCalledWith({
        where: { id: 1 },
      });
      expect(scoringService.calculateScores).toHaveBeenCalledWith(
        mockExam,
        mockSession.scoreDetails.answers,
      );
      expect(sessionRepository.save).toHaveBeenCalled();
      expect(result.totalScore).toBe(80);
    });

    it('should throw NotFoundException when session not found', async () => {
      // Arrange
      jest.spyOn(sessionRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.recalculateSession(999)).rejects.toThrow(
        new NotFoundException('Session with ID 999 not found'),
      );
    });

    it('should throw NotFoundException when exam not found', async () => {
      // Arrange
      jest.spyOn(sessionRepository, 'findOne').mockResolvedValue(mockSession as Session);
      jest.spyOn(examRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.recalculateSession(1)).rejects.toThrow(
        new NotFoundException('Exam with ID 1 not found'),
      );
    });
  });
});
