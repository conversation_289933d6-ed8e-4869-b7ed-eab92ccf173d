import { IsInt, IsArray, ValidateNested, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class SessionAnswerDto {
  @ApiProperty({ description: 'Course ID' })
  @IsInt()
  courseId: number;

  @ApiProperty({ description: 'Number of correct answers', minimum: 0 })
  @IsInt()
  @Min(0)
  correctAnswers: number;

  @ApiProperty({ description: 'Number of wrong answers', minimum: 0 })
  @IsInt()
  @Min(0)
  wrongAnswers: number;

  @ApiProperty({ description: 'Number of unanswered questions', minimum: 0 })
  @IsInt()
  @Min(0)
  unanswered: number;
}

export class CreateSessionDto {
  @ApiProperty({ description: 'Exam ID' })
  @IsInt()
  examId: number;

  @ApiProperty({ 
    description: 'List of answers for each course',
    type: [SessionAnswerDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SessionAnswerDto)
  answers: SessionAnswerDto[];
}
