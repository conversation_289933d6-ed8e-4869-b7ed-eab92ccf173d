import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, In } from 'typeorm';
import { Exam } from '../entities/exam.entity';
import { Course } from '../entities/course.entity';
import { CreateExamDto } from './dto/create-exam.dto';
import { UpdateExamDto } from './dto/update-exam.dto';

@Injectable()
export class ExamsService {
  constructor(
    @InjectRepository(Exam)
    private examRepository: Repository<Exam>,
    @InjectRepository(Course)
    private courseRepository: Repository<Course>,
  ) {}

  async create(createExamDto: CreateExamDto): Promise<Exam> {
    // Verify that all courses exist
    const courseIds = createExamDto.courses.map(c => c.courseId);
    const existingCourses = await this.courseRepository.find({
      where: { id: In(courseIds) },
    });

    if (existingCourses.length !== courseIds.length) {
      const existingIds = existingCourses.map(c => c.id);
      const missingIds = courseIds.filter(id => !existingIds.includes(id));
      throw new BadRequestException(`Courses with IDs ${missingIds.join(', ')} not found`);
    }

    const exam = this.examRepository.create(createExamDto);
    return this.examRepository.save(exam);
  }

  async findAll(): Promise<Exam[]> {
    return this.examRepository.find({
      relations: ['sessions'],
    });
  }

  async findOne(id: number): Promise<Exam> {
    const exam = await this.examRepository.findOne({
      where: { id },
      relations: ['sessions'],
    });

    if (!exam) {
      throw new NotFoundException(`Exam with ID ${id} not found`);
    }

    return exam;
  }

  async findOneWithCourseDetails(id: number): Promise<any> {
    const exam = await this.findOne(id);
    
    // Get course details for each course in the exam
    const courseIds = exam.courses.map(c => c.courseId);
    const courses = await this.courseRepository.find({
      where: { id: In(courseIds) },
      relations: ['courseGroup'],
    });

    // Combine exam course info with course details
    const coursesWithDetails = exam.courses.map(examCourse => {
      const courseDetail = courses.find(c => c.id === examCourse.courseId);
      return {
        ...examCourse,
        course: courseDetail,
      };
    });

    return {
      ...exam,
      coursesWithDetails,
    };
  }

  async update(id: number, updateExamDto: UpdateExamDto): Promise<Exam> {
    const exam = await this.findOne(id);

    // If courses are being updated, verify they exist
    if (updateExamDto.courses) {
      const courseIds = updateExamDto.courses.map(c => c.courseId);
      const existingCourses = await this.courseRepository.find({
        where: { id: In(courseIds) },
      });

      if (existingCourses.length !== courseIds.length) {
        const existingIds = existingCourses.map(c => c.id);
        const missingIds = courseIds.filter(id => !existingIds.includes(id));
        throw new BadRequestException(`Courses with IDs ${missingIds.join(', ')} not found`);
      }
    }

    Object.assign(exam, updateExamDto);
    return this.examRepository.save(exam);
  }

  async remove(id: number): Promise<void> {
    const exam = await this.findOne(id);
    await this.examRepository.remove(exam);
  }
}
