import { Is<PERSON><PERSON>, IsNotEmpty, IsArray, Validate<PERSON>ested, IsInt, Min } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';

export class ExamCourseDto {
  @ApiProperty({ description: 'Course ID' })
  @IsInt()
  courseId: number;

  @ApiProperty({ description: 'Number of questions for this course', minimum: 1 })
  @IsInt()
  @Min(1)
  questionCount: number;
}

export class CreateExamDto {
  @ApiProperty({ description: 'Name of the exam' })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({ 
    description: 'List of courses with question counts',
    type: [ExamCourseDto]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ExamCourseDto)
  courses: ExamCourseDto[];
}
