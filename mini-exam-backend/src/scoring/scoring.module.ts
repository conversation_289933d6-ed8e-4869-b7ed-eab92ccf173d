import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScoringService } from './scoring.service';
import { Course } from '../entities/course.entity';
import { CourseGroup } from '../entities/course-group.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Course, CourseGroup])],
  providers: [ScoringService],
  exports: [ScoringService],
})
export class ScoringModule {}
