#!/bin/bash

BASE_URL="http://localhost:3000"

echo "🧪 Testing Negative Score Calculation"
echo "======================================"

# 1. Register a user
echo "1. Registering user..."
TOKEN=$(curl -s -X POST $BASE_URL/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username": "student1", "password": "password123"}' | jq -r '.access_token')

if [ "$TOKEN" = "null" ] || [ -z "$TOKEN" ]; then
  echo "❌ Failed to register user"
  exit 1
fi
echo "✅ User registered, token: ${TOKEN:0:20}..."

# 2. Create a course group
echo "2. Creating course group..."
COURSE_GROUP=$(curl -s -X POST $BASE_URL/course-groups \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"name": "ریاضی و فیزیک"}')

COURSE_GROUP_ID=$(echo $COURSE_GROUP | jq -r '.id')
echo "✅ Course group created with ID: $COURSE_GROUP_ID"

# 3. Create a course with high negative coefficient
echo "3. Creating course with coefficients..."
COURSE=$(curl -s -X POST $BASE_URL/courses \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"name\": \"ریاضی\", \"courseGroupId\": $COURSE_GROUP_ID, \"positiveCoefficient\": 3, \"negativeCoefficient\": 1}")

COURSE_ID=$(echo $COURSE | jq -r '.id')
echo "✅ Course created with ID: $COURSE_ID (positive: 3, negative: 1)"

# 4. Create an exam
echo "4. Creating exam..."
EXAM=$(curl -s -X POST $BASE_URL/exams \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"name\": \"آزمون ریاضی\", \"courses\": [{\"courseId\": $COURSE_ID, \"questionCount\": 10}]}")

EXAM_ID=$(echo $EXAM | jq -r '.id')
echo "✅ Exam created with ID: $EXAM_ID"

# 5. Submit answers that should result in negative score
# Scenario: 2 correct, 8 wrong, 0 unanswered out of 10 questions
# Formula: (2*3 - 8*1) / (10*3) * 100 = (6-8)/30 * 100 = -2/30 * 100 = -6.67%
echo "5. Submitting answers (2 correct, 8 wrong, 0 unanswered)..."
echo "   Expected score: (2*3 - 8*1) / (10*3) * 100 = -6.67%"

SESSION=$(curl -s -X POST $BASE_URL/sessions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "{\"examId\": $EXAM_ID, \"answers\": [{\"courseId\": $COURSE_ID, \"correctAnswers\": 2, \"wrongAnswers\": 8, \"unanswered\": 0}]}")

echo "📊 Session Result:"
echo $SESSION | jq '.'

TOTAL_SCORE=$(echo $SESSION | jq -r '.totalScore')
COURSE_PERCENTAGE=$(echo $SESSION | jq -r '.scoreDetails.courseScores[0].percentage')

echo ""
echo "🎯 Results:"
echo "   Course Percentage: $COURSE_PERCENTAGE%"
echo "   Total Score: $TOTAL_SCORE"

if (( $(echo "$COURSE_PERCENTAGE < 0" | bc -l) )); then
  echo "✅ SUCCESS: Negative scores are working correctly!"
else
  echo "❌ FAILED: Expected negative score but got: $COURSE_PERCENTAGE"
fi

echo ""
echo "🧮 Manual Calculation Verification:"
echo "   Formula: (correct * pos_coeff - wrong * neg_coeff) / (total * pos_coeff) * 100"
echo "   Calculation: (2 * 3 - 8 * 1) / (10 * 3) * 100"
echo "   = (6 - 8) / 30 * 100"
echo "   = -2 / 30 * 100"
echo "   = -6.67%"
echo ""
echo "   Expected: -6.67%"
echo "   Actual: $COURSE_PERCENTAGE%"
