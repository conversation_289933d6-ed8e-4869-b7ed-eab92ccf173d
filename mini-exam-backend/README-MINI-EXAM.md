# Mini Exam Backend

A NestJS application for managing exams and calculating student scores, similar to Iran's Konkour exam system.

## Features

- **User Authentication**: Simple username/password authentication with JWT tokens
- **Course Management**: Manage course groups and courses with positive/negative coefficients (1-99)
- **Exam Management**: Create exams with course selection and question counts
- **Score Calculation**: Automatic calculation of:
  1. Course percentage
  2. Course group average
  3. Weighted course group score
  4. Total score
- **Session Management**: Submit exam answers and store calculated results
- **Statistics**: Get exam statistics and score distributions
- **API Documentation**: Swagger/OpenAPI documentation

## Database Schema

- **Users**: id, username, password, role
- **CourseGroups**: id, name
- **Courses**: id, name, course_group_id, positive_coefficient, negative_coefficient
- **Exams**: id, name, courses (JSON with courseId and questionCount)
- **Sessions**: id, exam_id, user_id, total_score, score_details (JSON)

## Setup

### Prerequisites

- Node.js (v16 or higher)
- PostgreSQL database
- npm or yarn

### Installation

1. <PERSON>lone the repository
2. Install dependencies:
```bash
npm install
```

3. Create a PostgreSQL database named `mini_exam`

4. Copy `.env` file and update database credentials:
```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_DATABASE=mini_exam

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Application Configuration
PORT=4000
NODE_ENV=development
```

5. Run the application:
```bash
# Development mode
npm run start:dev

# Production mode
npm run start:prod
```

## API Documentation

Once the application is running, visit:
- Application: http://localhost:3000
- Swagger Documentation: http://localhost:3000/api

## Score Calculation Formulas

1. **Course Percentage**:
   ```
   (correct_answers * positive_coeff - wrong_answers * negative_coeff) / (total_questions * positive_coeff) * 100
   ```
   *Note: Scores can be negative (important for accurate assessment like Konkour system)*

2. **Course Group Average**:
   ```
   Average of course percentages in the group
   ```

3. **Weighted Course Group Score**:
   ```
   group_average * average_coefficient_of_courses_in_group
   ```

4. **Total Score**:
   ```
   Average of weighted course group scores
   ```

**Important**: All scores (course, group, and total) can be negative values, which is essential for proper exam assessment where wrong answers are penalized.
