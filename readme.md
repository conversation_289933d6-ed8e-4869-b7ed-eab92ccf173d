ما می‌خوایم یه برنامه خیلی ساده با NestJS بنویسیم که یه سری ورودی داره از طریق یک فرم شبیه جدول داره و یه سری محاسبات انجام میده و نتیجه رو ذخیره می‌کنه.

دیتابیس هم می‌تونه postgres باشه. الان فقط بک‌اند رو لازم داریم. فرانت‌اند رو بعد می‌سازیم.

این برنامه می‌تونه نمرات دانشجوها رو محاسبه کنه (چیزی شبیه آزمون کنکور در ایران).

ما یک سری گروه درسی (دروس عمومی، دروس پایه و...) خواهیم داشت. هر گروه درسی یک سری درس داره و برای هر درس یک ضریب مثبت و ضریب منفی (بین ۱ تا ۹) رو مشخص می‌کنیم

ساختار دیتای این دو مورد به طول کلی:
هر گروه درسی: شناسه، نام
هر درس: شناسه، نام، شناسه گروه درسی، ضریب مثبت، ضریب منفی

بعد ما یک آزمون تعریف می‌کنیم که فقط دروس رو مشخص می‌کنیم به همراه تعداد سوالات هر درس.

حالا ما به ازای یک آزمون برای هر فرد یک فرم خواهیم داشت به نام «نشست» به این صورت که میگیم هر درس چند سوال درست و چند سوال غلط و چند بی‌پاسخ دارد.


ما ۴ محاسبه برای هر آزمون برای هر فرد داریم:
۱- محاسبه درصد درس
۲- محاسبه گروه درسی
۳- محاسبه گروه درسی با ضریب
۴- محاسبه کل


جزئیات محاسبه:
۱- درصد درس = (تعداد سوالات درست * ضریب مثبت - تعداد سوالات غلط * ضریب منفی) / (تعداد کل سوال‌های درس * ضریب مثبت) * 100
۲- امتیاز گروه درسی = میانگین درصد درس‌های یک گروه درسی
۳- امتیاز گروه درسی با ضریب = گروه درسی * ضریب گروه درسی
۴- کل = میانگین امتیاز گروه‌های درسی با میانگین ضریب درس‌های همان گروه درسی

حالا نتیجه نشست رو در یک جدول در دیتابیس ذخیره می‌کنیم تا بتونیم آمار کلی آزمون رو هم بگیریم.


پس جداولی که لازم داریم:
Users(id, username, password, role)
CourseGroups(id, name)
Courses(id, name, course_group_id)
Exams(id, name, course_ids)
Sessions(id, exam_id, user_id, total_score, score_details json/b)

نکته: احراز هویت خیلی ساده باشه. در حد یوزر و پسورد