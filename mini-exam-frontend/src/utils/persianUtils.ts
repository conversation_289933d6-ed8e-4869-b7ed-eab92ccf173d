export const convertDigitsToPersian = (input: string | number): string => {
  const persianDigits = ['۰', '۱', '۲', '۳', '۴', '۵', '۶', '۷', '۸', '۹'];
  const inputStr = String(input);
  const isNegative = inputStr.startsWith('-');

  let numberStr = isNegative ? inputStr.substring(1) : inputStr;

  const convertedNumber = numberStr.replace(/\d/g, (digit) => persianDigits[parseInt(digit)]);

  return isNegative ? `\u200E-${convertedNumber}` : convertedNumber;
};

// Generate random username
export const generateRandomUsername = (name?: string, lastName?: string): string => {
  const prefix = name && lastName
    ? `${name.toLowerCase()}_${lastName.toLowerCase()}`
    : 'student';

  const randomSuffix = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
  return `${prefix}_${randomSuffix}`;
};

// Generate random password
export const generateRandomPassword = (length: number = 8): string => {
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let password = '';

  for (let i = 0; i < length; i++) {
    password += charset.charAt(Math.floor(Math.random() * charset.length));
  }

  return password;
};
