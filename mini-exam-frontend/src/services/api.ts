import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  LoginDto,
  RegisterDto,
  LoginResponse,
  User,
  CourseGroup,
  CreateCourseGroupDto,
  Course,
  CreateCourseDto,
  Exam,
  CreateExamDto,
  Session,
  CreateSessionDto,
} from '../types';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:4000',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          localStorage.removeItem('access_token');
          localStorage.removeItem('user');
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(credentials: LoginDto): Promise<LoginResponse> {
    const response: AxiosResponse<LoginResponse> = await this.api.post('/auth/login', credentials);
    return response.data;
  }

  async register(userData: RegisterDto): Promise<LoginResponse> {
    const response: AxiosResponse<LoginResponse> = await this.api.post('/auth/register', userData);
    return response.data;
  }

  async getProfile(): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get('/auth/profile');
    return response.data;
  }

  // Course Groups endpoints
  async getCourseGroups(): Promise<CourseGroup[]> {
    const response: AxiosResponse<CourseGroup[]> = await this.api.get('/course-groups');
    return response.data;
  }

  async getCourseGroup(id: number): Promise<CourseGroup> {
    const response: AxiosResponse<CourseGroup> = await this.api.get(`/course-groups/${id}`);
    return response.data;
  }

  async createCourseGroup(data: CreateCourseGroupDto): Promise<CourseGroup> {
    const response: AxiosResponse<CourseGroup> = await this.api.post('/course-groups', data);
    return response.data;
  }

  async updateCourseGroup(id: number, data: Partial<CreateCourseGroupDto>): Promise<CourseGroup> {
    const response: AxiosResponse<CourseGroup> = await this.api.patch(`/course-groups/${id}`, data);
    return response.data;
  }

  async deleteCourseGroup(id: number): Promise<void> {
    await this.api.delete(`/course-groups/${id}`);
  }

  // Courses endpoints
  async getCourses(courseGroupId?: number): Promise<Course[]> {
    const params = courseGroupId ? { courseGroupId } : {};
    const response: AxiosResponse<Course[]> = await this.api.get('/courses', { params });
    return response.data;
  }

  async getCourse(id: number): Promise<Course> {
    const response: AxiosResponse<Course> = await this.api.get(`/courses/${id}`);
    return response.data;
  }

  async createCourse(data: CreateCourseDto): Promise<Course> {
    const response: AxiosResponse<Course> = await this.api.post('/courses', data);
    return response.data;
  }

  async updateCourse(id: number, data: Partial<CreateCourseDto>): Promise<Course> {
    const response: AxiosResponse<Course> = await this.api.patch(`/courses/${id}`, data);
    return response.data;
  }

  async deleteCourse(id: number): Promise<void> {
    await this.api.delete(`/courses/${id}`);
  }

  // Exams endpoints
  async getExams(): Promise<Exam[]> {
    const response: AxiosResponse<Exam[]> = await this.api.get('/exams');
    return response.data;
  }

  async getExam(id: number): Promise<Exam> {
    const response: AxiosResponse<Exam> = await this.api.get(`/exams/${id}`);
    return response.data;
  }

  async getExamWithDetails(id: number): Promise<Exam> {
    const response: AxiosResponse<Exam> = await this.api.get(`/exams/${id}/details`);
    return response.data;
  }

  async createExam(data: CreateExamDto): Promise<Exam> {
    const response: AxiosResponse<Exam> = await this.api.post('/exams', data);
    return response.data;
  }

  async updateExam(id: number, data: Partial<CreateExamDto>): Promise<Exam> {
    const response: AxiosResponse<Exam> = await this.api.patch(`/exams/${id}`, data);
    return response.data;
  }

  async deleteExam(id: number): Promise<void> {
    await this.api.delete(`/exams/${id}`);
  }

  // Sessions endpoints
  async getSessions(): Promise<Session[]> {
    const response: AxiosResponse<Session[]> = await this.api.get('/sessions');
    return response.data;
  }

  async getMySessions(): Promise<Session[]> {
    const response: AxiosResponse<Session[]> = await this.api.get('/sessions/my-sessions');
    return response.data;
  }

  async getSession(id: number): Promise<Session> {
    const response: AxiosResponse<Session> = await this.api.get(`/sessions/${id}`);
    return response.data;
  }

  async recalculateSession(id: number): Promise<Session> {
    const response: AxiosResponse<Session> = await this.api.patch(`/sessions/${id}/recalculate`);
    return response.data;
  }

  async getSessionsByExam(examId: number): Promise<Session[]> {
    const response: AxiosResponse<Session[]> = await this.api.get(`/sessions/by-exam/${examId}`);
    return response.data;
  }

  async getExamStatistics(examId: number): Promise<any> {
    const response: AxiosResponse<any> = await this.api.get(`/sessions/statistics/${examId}`);
    return response.data;
  }

  async createSession(data: CreateSessionDto): Promise<Session> {
    const response: AxiosResponse<Session> = await this.api.post('/sessions', data);
    return response.data;
  }

  async createSessionForUser(userId: number, data: CreateSessionDto): Promise<Session> {
    const response: AxiosResponse<Session> = await this.api.post(`/sessions/for-user/${userId}`, data);
    return response.data;
  }

  // Users endpoints
  async getUsers(): Promise<User[]> {
    const response: AxiosResponse<User[]> = await this.api.get('/users');
    return response.data;
  }

  async getUser(id: number): Promise<User> {
    const response: AxiosResponse<User> = await this.api.get(`/users/${id}`);
    return response.data;
  }

  async createUser(data: any): Promise<User> {
    const response: AxiosResponse<User> = await this.api.post('/users', data);
    return response.data;
  }

  async updateUser(id: number, data: any): Promise<User> {
    const response: AxiosResponse<User> = await this.api.patch(`/users/${id}`, data);
    return response.data;
  }

  async deleteUser(id: number): Promise<void> {
    await this.api.delete(`/users/${id}`);
  }
}

export const apiService = new ApiService();
export default apiService;
