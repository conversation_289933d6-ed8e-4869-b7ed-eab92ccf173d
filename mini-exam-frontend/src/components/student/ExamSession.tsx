import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Exam, Course, SessionAnswer, CreateSessionDto } from '../../types';
import apiService from '../../services/api';

interface ExamSessionProps {}

const ExamSession: React.FC<ExamSessionProps> = () => {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  
  const [exam, setExam] = useState<Exam | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [answers, setAnswers] = useState<SessionAnswer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchExamData = async () => {
      if (!examId) return;
      
      try {
        setIsLoading(true);
        const examData = await apiService.getExamWithDetails(parseInt(examId));
        setExam(examData);

        // Fetch course details
        const coursePromises = examData.courses.map(ec => 
          apiService.getCourse(ec.courseId)
        );
        const courseDetails = await Promise.all(coursePromises);
        setCourses(courseDetails);

        // Initialize answers
        const initialAnswers: SessionAnswer[] = examData.courses.map(ec => ({
          courseId: ec.courseId,
          correctAnswers: 0,
          wrongAnswers: 0,
          unanswered: ec.questionCount,
        }));
        setAnswers(initialAnswers);
      } catch (err: any) {
        setError('خطا در بارگذاری اطلاعات آزمون');
        console.error('Error fetching exam data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchExamData();
  }, [examId]);

  const handleAnswerChange = (courseId: number, field: keyof SessionAnswer, value: number) => {
    setAnswers(prev => prev.map(answer => {
      if (answer.courseId === courseId) {
        const updated = { ...answer, [field]: value };
        
        // Calculate unanswered based on total questions
        const examCourse = exam?.courses.find(ec => ec.courseId === courseId);
        if (examCourse) {
          updated.unanswered = examCourse.questionCount - updated.correctAnswers - updated.wrongAnswers;
        }
        
        return updated;
      }
      return answer;
    }));
  };

  const handleSubmit = async () => {
    if (!exam) return;

    // Validate answers
    const hasInvalidAnswers = answers.some(answer => {
      const examCourse = exam.courses.find(ec => ec.courseId === answer.courseId);
      if (!examCourse) return true;
      
      const total = answer.correctAnswers + answer.wrongAnswers + answer.unanswered;
      return total !== examCourse.questionCount || answer.correctAnswers < 0 || answer.wrongAnswers < 0;
    });

    if (hasInvalidAnswers) {
      setError('لطفاً تمام پاسخ‌ها را به درستی وارد کنید');
      return;
    }

    try {
      setIsSubmitting(true);
      const sessionData: CreateSessionDto = {
        examId: exam.id,
        answers: answers,
      };

      const result = await apiService.createSession(sessionData);
      navigate(`/session-result/${result.id}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ثبت پاسخ‌ها');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (!exam) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">آزمون یافت نشد</p>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Header */}
      <div className="bg-dark-800 rounded-lg p-6 mb-6">
        <div className="flex justify-between items-center">
          <button
            onClick={() => navigate('/exams')}
            className="bg-dark-700 hover:bg-dark-600 text-white px-4 py-2 rounded-md text-sm"
          >
            بازگشت به داشبورد
          </button>
          <h1 className="text-2xl font-bold text-primary-400 text-center flex-1">
            ورود اطلاعات: آزمون جامع شماره {exam.id}
          </h1>
        </div>
      </div>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      {/* Exam Table */}
      <div className="bg-dark-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-dark-700">
              <tr>
                <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">نام درس</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">گروه</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">کل سوالات</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">درست</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">غلط</th>
                <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">بی‌پاسخ</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-700">
              {exam.courses.map((examCourse) => {
                const course = courses.find(c => c.id === examCourse.courseId);
                const answer = answers.find(a => a.courseId === examCourse.courseId);
                
                if (!course || !answer) return null;

                return (
                  <tr key={course.id} className="hover:bg-dark-700/50">
                    <td className="px-6 py-4 text-white font-medium">
                      {course.name}
                    </td>
                    <td className="px-6 py-4 text-center text-dark-300">
                      {course.courseGroup?.name || 'عمومی'}
                    </td>
                    <td className="px-6 py-4 text-center text-dark-300">
                      {examCourse.questionCount}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <input
                        type="number"
                        min="0"
                        max={examCourse.questionCount}
                        value={answer.correctAnswers}
                        onChange={(e) => handleAnswerChange(
                          course.id, 
                          'correctAnswers', 
                          parseInt(e.target.value) || 0
                        )}
                        className="w-16 px-2 py-1 bg-dark-600 border border-dark-500 rounded text-center text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </td>
                    <td className="px-6 py-4 text-center">
                      <input
                        type="number"
                        min="0"
                        max={examCourse.questionCount}
                        value={answer.wrongAnswers}
                        onChange={(e) => handleAnswerChange(
                          course.id, 
                          'wrongAnswers', 
                          parseInt(e.target.value) || 0
                        )}
                        className="w-16 px-2 py-1 bg-dark-600 border border-dark-500 rounded text-center text-white focus:outline-none focus:ring-2 focus:ring-primary-500"
                      />
                    </td>
                    <td className="px-6 py-4 text-center">
                      <input
                        type="number"
                        min="0"
                        max={examCourse.questionCount}
                        value={answer.unanswered}
                        readOnly
                        className="w-16 px-2 py-1 bg-dark-700 border border-dark-600 rounded text-center text-dark-300 cursor-not-allowed"
                      />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Submit Button */}
      <div className="mt-8 text-center">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-8 py-3 rounded-lg text-lg font-medium"
        >
          {isSubmitting ? 'در حال محاسبه و ذخیره نتایج...' : 'محاسبه و ذخیره نتایج'}
        </button>
      </div>
    </div>
  );
};

export default ExamSession;
