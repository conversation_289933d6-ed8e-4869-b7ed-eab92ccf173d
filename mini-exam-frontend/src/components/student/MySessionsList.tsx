import React, { useState, useEffect } from "react";
import { Session } from "../../types";
import apiService from "../../services/api";
import { convertDigitsToPersian } from '../../utils/persianUtils';

// Helper function to safely convert to number and format
const formatScore = (score: any): string => {
  const numScore = typeof score === "string" ? parseFloat(score) : score;
  return convertDigitsToPersian(typeof numScore === "number" && !isNaN(numScore)
    ? numScore.toFixed(2)
    : "0.00");
};

const formatPercentage = (score: any): string => {
  const numScore = typeof score === "string" ? parseFloat(score) : score;
  return convertDigitsToPersian(typeof numScore === "number" && !isNaN(numScore)
    ? typeof score === 'number' && !isNaN(score) ? score.toFixed(2) : '0.0'
    : "0.0");
};

const MySessionsList: React.FC = () => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setIsLoading(true);
        const data = await apiService.getMySessions();
        setSessions(data);
      } catch (err: any) {
        setError("خطا در بارگذاری نتایج");
        console.error("Error fetching sessions:", err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessions();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-white mb-6">
          نتایج آزمون‌های من
        </h1>

        {sessions.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-dark-300 mb-4">
              شما هنوز در هیچ آزمونی شرکت نکرده‌اید
            </p>
            <a
              href="/exams"
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md inline-block"
            >
              مشاهده آزمون‌های موجود
            </a>
          </div>
        ) : (
          <div className="space-y-4">
            {sessions.map((session) => (
              <div key={session.id} className="bg-dark-700 rounded-lg p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">
                      آزمون شماره {convertDigitsToPersian(session.examId)}
                    </h3>
                    <p className="text-dark-300 text-sm">
                      تاریخ شرکت:{" "}
                      {convertDigitsToPersian(new Date(session.createdAt).toLocaleDateString("fa-IR"))}
                    </p>
                  </div>
                  <div className="text-left">
                    <div className="text-2xl font-bold text-primary-400">
                      {formatScore(session.totalScore)}%
                    </div>
                    <p className="text-dark-300 text-sm">نمره کل</p>
                  </div>
                </div>

                {session.scoreDetails && (
                  <div className="mt-4">
                    <h4 className="text-sm font-medium text-dark-200 mb-3">
                      جزئیات نمرات:
                    </h4>

                    {session.scoreDetails.courseScores &&
                      session.scoreDetails.courseScores.length > 0 && (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {session.scoreDetails.courseScores.map(
                            (courseScore, index) => {
                              const answer: any =
                                session?.scoreDetails?.answers?.[index] || {};
                              return (
                                <div
                                  key={index}
                                  className="bg-dark-600 p-3 rounded"
                                >
                                  <div className="flex justify-between items-center mb-2">
                                    <span className="text-white text-sm font-medium">
                                      {courseScore.courseName}
                                    </span>
                                    <span className="text-primary-400 font-bold">
                                      {formatPercentage(courseScore.percentage)}
                                      %
                                    </span>
                                  </div>
                                  <div className="text-xs text-dark-300 space-y-1">
                                    <div>
                                      درست: {convertDigitsToPersian(answer.correctAnswers)} | غلط:{" "}
                                      {convertDigitsToPersian(answer.wrongAnswers)} | بی‌پاسخ:{" "}
                                      {convertDigitsToPersian(answer.unanswered)}
                                    </div>
                                    <div>
                                      از{" "}
                                      {convertDigitsToPersian((answer.correctAnswers || 0) +
                                        (answer.wrongAnswers || 0) +
                                        (answer.unanswered || 0))}{" "}
                                      سوال
                                    </div>
                                  </div>
                                </div>
                              );
                            }
                          )}
                        </div>
                      )}

                    {session.scoreDetails.courseGroupScores &&
                      session.scoreDetails.courseGroupScores.length > 0 && (
                        <div className="mt-4">
                          <h5 className="text-sm font-medium text-dark-200 mb-2">
                            نمرات گروه‌های درسی:
                          </h5>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {session.scoreDetails.courseGroupScores.map(
                              (groupScore, index) => (
                                <div
                                  key={index}
                                  className="bg-dark-600 p-3 rounded"
                                >
                                  <div className="flex justify-between items-center">
                                    <span className="text-white text-sm">
                                      {groupScore.courseGroupName}
                                    </span>
                                    <div className="text-left">
                                      <div className="text-primary-400 font-bold text-sm">
                                        {formatPercentage(
                                          groupScore.weightedScore
                                        )}
                                        %
                                      </div>
                                      <div className="text-dark-300 text-xs">
                                        میانگین:{" "}
                                        {formatPercentage(
                                          groupScore.averageScore
                                        )}
                                        %
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default MySessionsList;
