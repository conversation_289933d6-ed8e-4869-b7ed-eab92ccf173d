import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Exam } from '../../types';
import apiService from '../../services/api';

const ExamList: React.FC = () => {
  const [exams, setExams] = useState<Exam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchExams = async () => {
      try {
        setIsLoading(true);
        const examsData = await apiService.getExams();
        setExams(examsData);
      } catch (err: any) {
        setError('خطا در بارگذاری آزمون‌ها');
        console.error('Error fetching exams:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchExams();
  }, []);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">{error}</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <h1 className="text-2xl font-bold text-white mb-6">آزمون‌های موجود</h1>
        
        {exams.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-dark-300">هیچ آزمونی موجود نیست</p>
          </div>
        ) : (
          <div className="space-y-4">
            {exams.map(exam => (
              <div key={exam.id} className="bg-dark-700 rounded-lg p-6 hover:bg-dark-600 transition-colors">
                <div className="flex justify-between items-center">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-2">{exam.name}</h3>
                    <p className="text-dark-300 text-sm">
                      تعداد دروس: {exam.courses.length}
                    </p>
                    <p className="text-dark-300 text-sm">
                      تاریخ ایجاد: {new Date(exam.createdAt).toLocaleDateString('fa-IR')}
                    </p>
                  </div>
                  <div className="flex space-x-3 space-x-reverse">
                    <Link
                      to={`/exam/${exam.id}`}
                      className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md font-medium"
                    >
                      شروع آزمون
                    </Link>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamList;
