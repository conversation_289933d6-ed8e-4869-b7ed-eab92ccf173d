import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { Session } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

// Helper function to safely convert to number and format
const formatScore = (score: any): string => {
  const numScore = typeof score === 'string' ? parseFloat(score) : score;
  return convertDigitsToPersian(typeof numScore === 'number' && !isNaN(numScore) ? numScore.toFixed(2) : '0.00');
};

const formatPercentage = (score: any): string => {
  const numScore = typeof score === 'string' ? parseFloat(score) : score;
  return convertDigitsToPersian(typeof numScore === 'number' && !isNaN(numScore) ? numScore.toFixed(1) : '0.0');
};

const SessionResult: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchSession = async () => {
      if (!sessionId) return;

      try {
        setIsLoading(true);
        // Try to get the specific session first
        try {
          const sessionData = await apiService.getSession(parseInt(sessionId));
          setSession(sessionData);
        } catch (sessionError) {
          // If that fails, try to find it in the user's sessions list
          const sessions = await apiService.getMySessions();
          const foundSession = sessions.find(s => s.id === parseInt(sessionId));

          if (foundSession) {
            setSession(foundSession);
          } else {
            setError('نتایج آزمون یافت نشد');
          }
        }
      } catch (err: any) {
        setError('خطا در بارگذاری نتایج');
        console.error('Error fetching session:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSession();
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">نتایج آزمون</h1>
          <Link
            to="/exams"
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md"
          >
            بازگشت به آزمون‌ها
          </Link>
        </div>

        {error ? (
          <div className="text-center py-12">
            <p className="text-red-400 mb-4">{error}</p>
            <Link
              to="/exams"
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md inline-block"
            >
              بازگشت به آزمون‌ها
            </Link>
          </div>
        ) : session ? (
          <div>
            {/* Success Message */}
            <div className="bg-green-900/50 border border-green-500 text-green-200 px-6 py-4 rounded-lg mb-6">
              <h2 className="text-xl font-bold mb-2">✅ پاسخ‌های شما با موفقیت ثبت شد!</h2>
              <p>نتایج شما محاسبه و در سیستم ذخیره شده است.</p>
            </div>

            {/* Overall Score */}
            <div className="bg-dark-700 rounded-lg p-6 mb-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-white mb-4">نمره کل شما</h3>
                <div className="text-4xl font-bold text-primary-400 mb-2 number-ltr">
                  {formatScore(session.totalScore)}%
                </div>
                <p className="text-dark-300">
                  تاریخ آزمون: {convertDigitsToPersian(new Date(session.createdAt).toLocaleDateString('fa-IR'))}
                </p>
              </div>
            </div>

            {/* Detailed Results */}
            {session.scoreDetails && (
              <div className="space-y-6">
                {/* Course Scores */}
                {session.scoreDetails.courseScores && session.scoreDetails.courseScores.length > 0 && (
                  <div className="bg-dark-700 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">نمرات دروس</h3>
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead className="bg-dark-600">
                          <tr>
                            <th className="px-4 py-3 text-right text-sm font-medium text-dark-200">نام درس</th>
                            <th className="px-4 py-3 text-center text-sm font-medium text-dark-200">درصد</th>
                            <th className="px-4 py-3 text-center text-sm font-medium text-dark-200">درست</th>
                            <th className="px-4 py-3 text-center text-sm font-medium text-dark-200">غلط</th>
                            <th className="px-4 py-3 text-center text-sm font-medium text-dark-200">بی‌پاسخ</th>
                            <th className="px-4 py-3 text-center text-sm font-medium text-dark-200">کل سوالات</th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-dark-600">
                          {session.scoreDetails.courseScores.map((courseScore, index) => (
                            <tr key={index} className="hover:bg-dark-600/50">
                              <td className="px-4 py-3 text-white font-medium">{courseScore.courseName}</td>
                              <td className="px-4 py-3 text-center">
                                <span className={`font-bold number-ltr ${parseFloat(formatPercentage(courseScore.percentage)) >= 50 ? 'text-green-400' : 'text-red-400'}`}>
                                  {formatPercentage(courseScore.percentage)}%
                                </span>
                              </td>
                              <td className="px-4 py-3 text-center text-green-400">{convertDigitsToPersian(courseScore.correctAnswers)}</td>
                              <td className="px-4 py-3 text-center text-red-400">{convertDigitsToPersian(courseScore.wrongAnswers)}</td>
                              <td className="px-4 py-3 text-center text-dark-300">{convertDigitsToPersian(courseScore.unanswered)}</td>
                              <td className="px-4 py-3 text-center text-dark-300">{convertDigitsToPersian(courseScore.totalQuestions)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}

                {/* Course Group Scores */}
                {session.scoreDetails.courseGroupScores && session.scoreDetails.courseGroupScores.length > 0 && (
                  <div className="bg-dark-700 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">نمرات گروه‌های درسی</h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {session.scoreDetails.courseGroupScores.map((groupScore, index) => (
                        <div key={index} className="bg-dark-600 p-4 rounded-lg">
                          <h4 className="text-white font-medium mb-3">{groupScore.courseGroupName}</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between">
                              <span className="text-dark-300">نمره وزنی:</span>
                              <span className="text-primary-400 font-bold number-ltr">
                                {formatPercentage(groupScore.weightedScore)}%
                              </span>
                            </div>
                            <div className="flex justify-between">
                              <span className="text-dark-300">میانگین:</span>
                              <span className="text-dark-200 number-ltr">
                                {formatPercentage(groupScore.averageScore)}%
                              </span>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Action Buttons */}
            <div className="mt-8 text-center space-x-4 space-x-reverse">
              <Link
                to="/my-sessions"
                className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md inline-block"
              >
                مشاهده همه نتایج
              </Link>
              <Link
                to="/exams"
                className="bg-dark-600 hover:bg-dark-500 text-white px-6 py-2 rounded-md inline-block"
              >
                آزمون‌های جدید
              </Link>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <p className="text-dark-300">در حال بارگذاری نتایج...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionResult;
