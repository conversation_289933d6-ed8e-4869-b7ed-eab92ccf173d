import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Session, Course } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const AdminSessionResult: React.FC = () => {
  const { sessionId } = useParams<{ sessionId: string }>();
  const navigate = useNavigate();
  const [session, setSession] = useState<Session | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      if (!sessionId) {
        setError('شناسه جلسه نامعتبر است');
        setIsLoading(false);
        return;
      }

      try {
        const [sessionData, coursesData] = await Promise.all([
          apiService.getSession(parseInt(sessionId)),
          apiService.getCourses(),
        ]);

        setSession(sessionData);
        setCourses(coursesData);
      } catch (err: any) {
        setError(err.response?.data?.message || 'خطا در بارگذاری اطلاعات');
        console.error('Error fetching session data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [sessionId]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400 mb-4">{error || 'جلسه یافت نشد'}</p>
        <button
          onClick={() => navigate('/admin/students')}
          className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded"
        >
          بازگشت به لیست دانش‌آموزان
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-3xl font-bold text-white">نتایج آزمون</h1>
        <div className="flex gap-2">
          <button
            onClick={() => navigate('/admin/students')}
            className="bg-dark-600 hover:bg-dark-500 text-white px-4 py-2 rounded transition-colors"
          >
            بازگشت به دانش‌آموزان
          </button>
          <button
            onClick={() => navigate('/admin/sessions')}
            className="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded transition-colors"
          >
            مشاهده همه جلسات
          </button>
        </div>
      </div>

      {/* Session Info */}
      <div className="bg-dark-800 p-6 rounded-lg mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <h2 className="text-xl font-semibold text-white mb-3">اطلاعات آزمون</h2>
            <p className="text-dark-300 mb-2"><strong>نام آزمون:</strong> {session.exam?.name}</p>
            <p className="text-dark-300"><strong>تاریخ برگزاری:</strong> {new Date(session.createdAt).toLocaleDateString('fa-IR')}</p>
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white mb-3">اطلاعات دانش‌آموز</h2>
            <p className="text-dark-300 mb-2"><strong>نام:</strong> {session.user?.name} {session.user?.lastName}</p>
            <p className="text-dark-300"><strong>نام کاربری:</strong> {session.user?.username}</p>
          </div>
          <div>
            <h2 className="text-xl font-semibold text-white mb-3">نتیجه کلی</h2>
            <p className="text-3xl font-bold text-primary-400 mb-2">
              {convertDigitsToPersian(session.totalScore.toFixed(2))}
            </p>
            <p className="text-dark-300">از ۱۰۰ نمره</p>
          </div>
        </div>
      </div>

      {/* Course Scores */}
      <div className="bg-dark-800 rounded-lg overflow-hidden shadow-lg mb-6">
        <div className="bg-dark-700 px-6 py-4">
          <h2 className="text-xl font-semibold text-white">نمرات دروس</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-dark-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-dark-300 uppercase tracking-wider">
                  درس
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  گروه درسی
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  پاسخ صحیح
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  پاسخ غلط
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  بدون پاسخ
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  درصد
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-700">
              {session.scoreDetails.courseScores.map((courseScore) => {
                const course = courses.find(c => c.id === courseScore.courseId);
                const answer = session.scoreDetails.answers?.find(a => a.courseId === courseScore.courseId);
                
                if (!course || !answer) return null;

                return (
                  <tr key={course.id} className="hover:bg-dark-700/50">
                    <td className="px-6 py-4 text-white font-medium">
                      {courseScore.courseName}
                    </td>
                    <td className="px-6 py-4 text-center text-dark-300">
                      {course.courseGroup?.name || 'عمومی'}
                    </td>
                    <td className="px-6 py-4 text-center text-green-400">
                      {convertDigitsToPersian(answer.correctAnswers)}
                    </td>
                    <td className="px-6 py-4 text-center text-red-400">
                      {convertDigitsToPersian(answer.wrongAnswers)}
                    </td>
                    <td className="px-6 py-4 text-center text-yellow-400">
                      {convertDigitsToPersian(answer.unanswered)}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <span className={`font-bold ${
                        courseScore.percentage >= 70 ? 'text-green-400' :
                        courseScore.percentage >= 50 ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {convertDigitsToPersian(courseScore.percentage.toFixed(1))}%
                      </span>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      {/* Course Group Scores */}
      {session.scoreDetails.courseGroupScores && session.scoreDetails.courseGroupScores.length > 0 && (
        <div className="bg-dark-800 rounded-lg overflow-hidden shadow-lg">
          <div className="bg-dark-700 px-6 py-4">
            <h2 className="text-xl font-semibold text-white">نمرات گروه‌های درسی</h2>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-dark-700">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-dark-300 uppercase tracking-wider">
                    گروه درسی
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                    میانگین نمره
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                    نمره وزنی
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-700">
                {session.scoreDetails.courseGroupScores.map((groupScore) => (
                  <tr key={groupScore.courseGroupId} className="hover:bg-dark-700/50">
                    <td className="px-6 py-4 text-white font-medium">
                      {groupScore.courseGroupName}
                    </td>
                    <td className="px-6 py-4 text-center text-dark-300">
                      {convertDigitsToPersian(groupScore.averageScore.toFixed(2))}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <span className={`font-bold ${
                        groupScore.weightedScore >= 70 ? 'text-green-400' :
                        groupScore.weightedScore >= 50 ? 'text-yellow-400' : 'text-red-400'
                      }`}>
                        {convertDigitsToPersian(groupScore.weightedScore.toFixed(2))}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminSessionResult;
