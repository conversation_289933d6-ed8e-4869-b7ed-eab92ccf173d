import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { User, UserRole } from '../../types';
import apiService from '../../services/api';
import { generateRandomUsername, generateRandomPassword } from '../../utils/persianUtils';

interface StudentFormProps {
  isEdit?: boolean;
}

const StudentForm: React.FC<StudentFormProps> = ({ isEdit = false }) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<Partial<User>>({
    username: '',
    password: '',
    name: '',
    lastName: '',
    role: UserRole.USER,
  });
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [generatedCredentials, setGeneratedCredentials] = useState<{username: string, password: string} | null>(null);

  useEffect(() => {
    if (isEdit && id) {
      setLoading(true);
      apiService.getUser(parseInt(id))
        .then((user) => {
          setFormData({
            username: user.username,
            name: user.name,
            lastName: user.lastName,
            role: user.role,
            // Password is not pre-filled for security reasons
          });
        })
        .catch((err) => {
          setError('Failed to fetch student data.');
          console.error(err);
        })
        .finally(() => {
          setLoading(false);
        });
    }
  }, [isEdit, id]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const generateCredentials = () => {
    const username = generateRandomUsername(formData.name, formData.lastName);
    const password = generateRandomPassword();
    
    setFormData(prev => ({
      ...prev,
      username,
      password,
    }));
    
    setGeneratedCredentials({ username, password });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Ensure role is always USER for students
      const studentData = { ...formData, role: UserRole.USER };

      if (isEdit && id) {
        if (!studentData.password) {
          delete studentData.password;
        }
        await apiService.updateUser(parseInt(id), studentData);
        setSuccess('Student updated successfully!');
      } else {
        // For new students, generate credentials if not provided
        if (!studentData.username || !studentData.password) {
          const username = studentData.username || generateRandomUsername(studentData.name, studentData.lastName);
          const password = studentData.password || generateRandomPassword();
          
          studentData.username = username;
          studentData.password = password;
          
          setGeneratedCredentials({ username, password });
        }
        
        await apiService.createUser(studentData);
        setSuccess('Student created successfully!');
        
        if (!isEdit) {
          // Clear form after creation but keep generated credentials visible
          setFormData({
            username: '',
            password: '',
            name: '',
            lastName: '',
            role: UserRole.USER,
          });
        }
      }
      
      // Redirect after a short delay to show success message
      setTimeout(() => {
        navigate('/admin/students');
      }, 2000);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to save student.');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  if (loading && isEdit) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">Loading student data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-2xl">
      <h1 className="text-3xl font-bold text-white mb-6">
        {isEdit ? 'Edit Student' : 'Add New Student'}
      </h1>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-900/50 border border-green-500 text-green-200 px-4 py-3 rounded mb-4">
          {success}
        </div>
      )}

      {generatedCredentials && (
        <div className="bg-blue-900/50 border border-blue-500 text-blue-200 px-4 py-3 rounded mb-4">
          <h3 className="font-bold mb-2">Generated Credentials:</h3>
          <p><strong>Username:</strong> {generatedCredentials.username}</p>
          <p><strong>Password:</strong> {generatedCredentials.password}</p>
          <p className="text-sm mt-2 text-blue-300">Please save these credentials and share them with the student.</p>
        </div>
      )}

      <form onSubmit={handleSubmit} className="bg-dark-800 p-6 rounded-lg shadow-lg">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label htmlFor="name" className="block text-dark-300 text-sm font-bold mb-2">
              Name:
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name || ''}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
              required
            />
          </div>
          <div>
            <label htmlFor="lastName" className="block text-dark-300 text-sm font-bold mb-2">
              Last Name:
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName || ''}
              onChange={handleChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
              required
            />
          </div>
        </div>

        <div className="mb-4">
          <label htmlFor="username" className="block text-dark-300 text-sm font-bold mb-2">
            Username:
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username || ''}
              onChange={handleChange}
              className="flex-1 px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
              placeholder="Leave empty to auto-generate"
            />
            {!isEdit && (
              <button
                type="button"
                onClick={generateCredentials}
                className="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded transition-colors"
              >
                Generate
              </button>
            )}
          </div>
        </div>

        <div className="mb-6">
          <label htmlFor="password" className="block text-dark-300 text-sm font-bold mb-2">
            Password: {isEdit && <span className="text-dark-400 text-xs">(Leave blank to keep current password)</span>}
          </label>
          <input
            type="password"
            id="password"
            name="password"
            value={formData.password || ''}
            onChange={handleChange}
            className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded text-white focus:outline-none focus:border-primary-500"
            placeholder={isEdit ? "Leave empty to keep current" : "Leave empty to auto-generate"}
            {...(!isEdit && !formData.password && { required: false })}
          />
        </div>

        <div className="flex items-center justify-between">
          <button
            type="button"
            onClick={() => navigate('/admin/students')}
            className="bg-dark-600 hover:bg-dark-500 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={loading}
            className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-800 text-white font-bold py-2 px-4 rounded transition-colors"
          >
            {loading ? 'Saving...' : (isEdit ? 'Update Student' : 'Create Student')}
          </button>
        </div>
      </form>
    </div>
  );
};

export default StudentForm;
