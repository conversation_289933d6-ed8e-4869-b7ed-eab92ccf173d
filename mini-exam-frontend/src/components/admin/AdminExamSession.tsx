import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Exam, Course, User, UserRole, CreateSessionDto, SessionAnswer } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const AdminExamSession: React.FC = () => {
  const { examId, studentId } = useParams<{ examId: string; studentId: string }>();
  const navigate = useNavigate();
  
  const [exam, setExam] = useState<Exam | null>(null);
  const [student, setStudent] = useState<User | null>(null);
  const [courses, setCourses] = useState<Course[]>([]);
  const [answers, setAnswers] = useState<SessionAnswer[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      if (!examId || !studentId) {
        setError('Invalid exam or student ID');
        setIsLoading(false);
        return;
      }

      try {
        const [examData, studentData, coursesData] = await Promise.all([
          apiService.getExamWithDetails(parseInt(examId)),
          apiService.getUser(parseInt(studentId)),
          apiService.getCourses(),
        ]);

        // Verify student is actually a student (USER role)
        if (studentData.role !== UserRole.USER) {
          setError('Selected user is not a student');
          setIsLoading(false);
          return;
        }

        setExam(examData);
        setStudent(studentData);
        setCourses(coursesData);

        // Initialize answers for each course in the exam
        const initialAnswers: SessionAnswer[] = examData.courses.map(examCourse => ({
          courseId: examCourse.courseId,
          correctAnswers: 0,
          wrongAnswers: 0,
          unanswered: examCourse.questionCount,
        }));

        setAnswers(initialAnswers);
      } catch (err: any) {
        setError(err.response?.data?.message || 'خطا در بارگذاری اطلاعات');
        console.error('Error fetching data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [examId, studentId]);

  const handleAnswerChange = (courseId: number, field: keyof SessionAnswer, value: number) => {
    if (value < 0) return;

    const examCourse = exam?.courses.find(ec => ec.courseId === courseId);
    if (!examCourse) return;

    setAnswers(prev => prev.map(answer => {
      if (answer.courseId === courseId) {
        const newAnswer = { ...answer, [field]: value };
        
        // Auto-calculate unanswered questions
        if (field !== 'unanswered') {
          const total = newAnswer.correctAnswers + newAnswer.wrongAnswers;
          newAnswer.unanswered = Math.max(0, examCourse.questionCount - total);
        } else {
          // If unanswered is changed, adjust other fields proportionally
          const answered = examCourse.questionCount - value;
          const currentAnswered = answer.correctAnswers + answer.wrongAnswers;
          
          if (currentAnswered > 0 && answered >= 0) {
            const ratio = answered / currentAnswered;
            newAnswer.correctAnswers = Math.floor(answer.correctAnswers * ratio);
            newAnswer.wrongAnswers = answered - newAnswer.correctAnswers;
          }
        }

        return newAnswer;
      }
      return answer;
    }));
  };

  const handleSubmit = async () => {
    if (!exam || !student) return;

    // Validate answers
    const hasInvalidAnswers = answers.some(answer => {
      const examCourse = exam.courses.find(ec => ec.courseId === answer.courseId);
      if (!examCourse) return true;
      
      const total = answer.correctAnswers + answer.wrongAnswers + answer.unanswered;
      return total !== examCourse.questionCount || answer.correctAnswers < 0 || answer.wrongAnswers < 0;
    });

    if (hasInvalidAnswers) {
      setError('لطفاً تمام پاسخ‌ها را به درستی وارد کنید');
      return;
    }

    try {
      setIsSubmitting(true);
      const sessionData: CreateSessionDto = {
        examId: exam.id,
        answers: answers,
      };

      // Create session for the specific student
      const result = await apiService.createSessionForUser(student.id, sessionData);
      navigate(`/admin/session-result/${result.id}`);
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ثبت پاسخ‌ها');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  if (!exam || !student) {
    return (
      <div className="text-center py-12">
        <p className="text-red-400">{error || 'آزمون یا دانش‌آموز یافت نشد'}</p>
        <button
          onClick={() => navigate('/admin/students')}
          className="mt-4 bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded"
        >
          بازگشت به لیست دانش‌آموزان
        </button>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <div className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-3xl font-bold text-white">ثبت نتایج آزمون</h1>
          <button
            onClick={() => navigate('/admin/students')}
            className="bg-dark-600 hover:bg-dark-500 text-white px-4 py-2 rounded transition-colors"
          >
            بازگشت
          </button>
        </div>
        
        <div className="bg-dark-800 p-4 rounded-lg mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h2 className="text-xl font-semibold text-white mb-2">اطلاعات آزمون</h2>
              <p className="text-dark-300"><strong>نام آزمون:</strong> {exam.name}</p>
              <p className="text-dark-300"><strong>تعداد دروس:</strong> {convertDigitsToPersian(exam.courses.length)}</p>
            </div>
            <div>
              <h2 className="text-xl font-semibold text-white mb-2">اطلاعات دانش‌آموز</h2>
              <p className="text-dark-300"><strong>نام:</strong> {student.name} {student.lastName}</p>
              <p className="text-dark-300"><strong>نام کاربری:</strong> {student.username}</p>
            </div>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="bg-dark-800 rounded-lg overflow-hidden shadow-lg">
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead className="bg-dark-700">
              <tr>
                <th className="px-6 py-3 text-right text-xs font-medium text-dark-300 uppercase tracking-wider">
                  درس
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  گروه درسی
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  تعداد سوال
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  پاسخ صحیح
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  پاسخ غلط
                </th>
                <th className="px-6 py-3 text-center text-xs font-medium text-dark-300 uppercase tracking-wider">
                  بدون پاسخ
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-dark-700">
              {exam.courses.map((examCourse) => {
                const course = courses.find(c => c.id === examCourse.courseId);
                const answer = answers.find(a => a.courseId === examCourse.courseId);
                
                if (!course || !answer) return null;

                return (
                  <tr key={course.id} className="hover:bg-dark-700/50">
                    <td className="px-6 py-4 text-white font-medium">
                      {course.name}
                    </td>
                    <td className="px-6 py-4 text-center text-dark-300">
                      {course.courseGroup?.name || 'عمومی'}
                    </td>
                    <td className="px-6 py-4 text-center text-dark-300">
                      {convertDigitsToPersian(examCourse.questionCount)}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <input
                        type="number"
                        min="0"
                        max={examCourse.questionCount}
                        value={answer.correctAnswers}
                        onChange={(e) => handleAnswerChange(
                          course.id, 
                          'correctAnswers', 
                          parseInt(e.target.value) || 0
                        )}
                        className="w-20 px-2 py-1 bg-dark-600 border border-dark-500 rounded text-white text-center focus:outline-none focus:border-primary-500"
                      />
                    </td>
                    <td className="px-6 py-4 text-center">
                      <input
                        type="number"
                        min="0"
                        max={examCourse.questionCount}
                        value={answer.wrongAnswers}
                        onChange={(e) => handleAnswerChange(
                          course.id, 
                          'wrongAnswers', 
                          parseInt(e.target.value) || 0
                        )}
                        className="w-20 px-2 py-1 bg-dark-600 border border-dark-500 rounded text-white text-center focus:outline-none focus:border-primary-500"
                      />
                    </td>
                    <td className="px-6 py-4 text-center">
                      <input
                        type="number"
                        min="0"
                        max={examCourse.questionCount}
                        value={answer.unanswered}
                        onChange={(e) => handleAnswerChange(
                          course.id, 
                          'unanswered', 
                          parseInt(e.target.value) || 0
                        )}
                        className="w-20 px-2 py-1 bg-dark-600 border border-dark-500 rounded text-white text-center focus:outline-none focus:border-primary-500"
                      />
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mt-6 flex justify-center">
        <button
          onClick={handleSubmit}
          disabled={isSubmitting}
          className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-800 text-white font-bold py-3 px-8 rounded-lg transition-colors"
        >
          {isSubmitting ? 'در حال ثبت...' : 'ثبت نتایج آزمون'}
        </button>
      </div>
    </div>
  );
};

export default AdminExamSession;
