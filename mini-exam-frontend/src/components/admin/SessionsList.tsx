import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Session } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const SessionsList: React.FC = () => {
  const navigate = useNavigate();
  const [sessions, setSessions] = useState<Session[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [recalculatingId, setRecalculatingId] = useState<number | null>(null);

  useEffect(() => {
    const fetchSessions = async () => {
      try {
        setIsLoading(true);
        const data = await apiService.getSessions();
        setSessions(data);
      } catch (err: any) {
        setError('خطا در بارگذاری جلسات آزمون');
        console.error('Error fetching sessions:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSessions();
  }, []);

  const handleRecalculate = async (sessionId: number) => {
    try {
      setRecalculatingId(sessionId);
      setError('');
      
      const updatedSession = await apiService.recalculateSession(sessionId);
      
      // Update the session in the list
      setSessions(prev => 
        prev.map(session => 
          session.id === sessionId ? updatedSession : session
        )
      );
      
      // Show success message (you could add a toast notification here)
      alert('نمرات با موفقیت محاسبه مجدد شد');
      
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در محاسبه مجدد نمرات');
      console.error('Error recalculating session:', err);
    } finally {
      setRecalculatingId(null);
    }
  };

  const formatScore = (score: number): string => {
    return convertDigitsToPersian(typeof score === 'number' && !isNaN(score) ? score.toFixed(2) : '0.0');
  };

  const formatPercentage = (percentage: number): string => {
    return convertDigitsToPersian(typeof percentage === 'number' && !isNaN(percentage) ? percentage.toFixed(1) : '0.0');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">مدیریت جلسات آزمون</h1>
          <button
            onClick={() => navigate('/admin/sessions/create')}
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md font-medium transition-colors"
          >
            + ایجاد جلسه دستی
          </button>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {sessions.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-dark-300">هیچ جلسه آزمونی موجود نیست</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-dark-700">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">شناسه جلسه</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">نام کاربری</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">نام</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">نام خانوادگی</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">شناسه آزمون</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">نمره کل</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">تاریخ شرکت</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">عملیات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-700">
                {sessions.map((session) => (
                  <React.Fragment key={session.id}>
                    <tr className="hover:bg-dark-700/50">
                      <td className="px-6 py-4 text-dark-300">{convertDigitsToPersian(session.id)}</td>
                      <td className="px-6 py-4 text-white font-medium">
                        {session.user?.username || 'نامشخص'}
                      </td>
                      <td className="px-6 py-4 text-dark-300">
                        {session.user?.name || 'نامشخص'}
                      </td>
                      <td className="px-6 py-4 text-dark-300">
                        {session.user?.lastName || 'نامشخص'}
                      </td>
                      <td className="px-6 py-4 text-center text-dark-300">
                        {convertDigitsToPersian(session.examId)}
                      </td>
                      <td className="px-6 py-4 text-center">
                        <span className={`font-bold ${parseFloat(formatScore(session.totalScore)) >= 50 ? 'text-green-400' : 'text-red-400'}`}>
                          {formatScore(session.totalScore)}%
                        </span>
                      </td>
                      <td className="px-6 py-4 text-dark-300">
                        {convertDigitsToPersian(new Date(session.createdAt).toLocaleDateString('fa-IR'))}
                      </td>
                      <td className="px-6 py-4 text-center">
                        <button
                          onClick={() => handleRecalculate(session.id)}
                          disabled={recalculatingId === session.id}
                          className={`px-4 py-2 rounded text-sm font-medium ${
                            recalculatingId === session.id
                              ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                              : 'bg-blue-600 hover:bg-blue-700 text-white'
                          }`}
                        >
                          {recalculatingId === session.id ? (
                            <div className="flex items-center">
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                              در حال محاسبه...
                            </div>
                          ) : (
                            'محاسبه مجدد'
                          )}
                        </button>
                      </td>
                    </tr>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default SessionsList;
