import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Exam } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const ExamList: React.FC = () => {
  const [exams, setExams] = useState<Exam[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchExams = async () => {
      try {
        setIsLoading(true);
        const data = await apiService.getExams();
        setExams(data);
      } catch (err: any) {
        setError('خطا در بارگذاری آزمون‌ها');
        console.error('Error fetching exams:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchExams();
  }, []);

  const handleDelete = async (id: number) => {
    if (!window.confirm('آیا از حذف این آزمون اطمینان دارید؟')) {
      return;
    }

    try {
      await apiService.deleteExam(id);
      setExams(prev => prev.filter(e => e.id !== id));
    } catch (err: any) {
      setError('خطا در حذف آزمون');
      console.error('Error deleting exam:', err);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">مدیریت آزمون‌ها</h1>
          <Link
            to="/admin/exams/new"
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md"
          >
            ایجاد آزمون جدید
          </Link>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {exams.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-dark-300">هیچ آزمونی موجود نیست</p>
            <Link
              to="/admin/exams/new"
              className="mt-4 inline-block bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md"
            >
              ایجاد اولین آزمون
            </Link>
          </div>
        ) : (
          <div className="space-y-4">
            {exams.map(exam => (
              <div key={exam.id} className="bg-dark-700 rounded-lg p-6">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-white mb-2">{exam.name}</h3>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="text-dark-300">
                        <span className="font-medium">شناسه:</span> {convertDigitsToPersian(exam.id)}
                      </div>
                      <div className="text-dark-300">
                        <span className="font-medium">تعداد دروس:</span> {convertDigitsToPersian(exam.courses.length)}
                      </div>
                      <div className="text-dark-300">
                        <span className="font-medium">تاریخ ایجاد:</span> {convertDigitsToPersian(new Date(exam.createdAt).toLocaleDateString('fa-IR'))}
                      </div>
                    </div>
                    
                    <div className="mt-4">
                      <h4 className="text-sm font-medium text-dark-200 mb-2">دروس آزمون:</h4>
                      <div className="flex flex-wrap gap-2">
                        {exam.courses.map((examCourse, index) => (
                          <span
                            key={index}
                            className="bg-dark-600 text-dark-200 px-2 py-1 rounded text-xs"
                          >
                            درس {convertDigitsToPersian(examCourse.courseId)} ({convertDigitsToPersian(examCourse.questionCount)} سوال)
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex space-x-2 space-x-reverse mr-4">
                    <Link
                      to={`/admin/exams/${exam.id}/leaderboard`}
                      className="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded text-sm"
                    >
                      نتایج
                    </Link>
                    <Link
                      to={`/admin/exams/edit/${exam.id}`}
                      className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded text-sm"
                    >
                      ویرایش
                    </Link>
                    <button
                      onClick={() => handleDelete(exam.id)}
                      className="bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded text-sm"
                    >
                      حذف
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ExamList;
