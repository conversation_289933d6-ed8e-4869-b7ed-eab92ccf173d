import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { CreateCourseGroupDto, CourseGroup } from '../../types';
import apiService from '../../services/api';

interface CourseGroupFormProps {
  isEdit?: boolean;
}

const CourseGroupForm: React.FC<CourseGroupFormProps> = ({ isEdit = false }) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  const [formData, setFormData] = useState<CreateCourseGroupDto>({
    name: '',
    positiveCoefficient: 1, // Default value
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    if (isEdit && id) {
      const fetchCourseGroup = async () => {
        try {
          const courseGroup = await apiService.getCourseGroup(parseInt(id));
          setFormData({ 
            name: courseGroup.name,
            positiveCoefficient: courseGroup.positiveCoefficient,
          });
        } catch (err: any) {
          setError('خطا در بارگذاری اطلاعات گروه درسی');
          console.error('Error fetching course group:', err);
        }
      };

      fetchCourseGroup();
    }
  }, [isEdit, id]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('نام گروه درسی الزامی است');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      if (isEdit && id) {
        await apiService.updateCourseGroup(parseInt(id), formData);
      } else {
        await apiService.createCourseGroup(formData);
      }

      navigate('/admin/course-groups');
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ذخیره گروه درسی');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">
            {isEdit ? 'ویرایش گروه درسی' : 'ایجاد گروه درسی جدید'}
          </h1>
          <button
            onClick={() => navigate('/admin/course-groups')}
            className="bg-dark-700 hover:bg-dark-600 text-white px-4 py-2 rounded-md"
          >
            بازگشت
          </button>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-dark-200 mb-2">
              نام گروه درسی
            </label>
            <input
              type="text"
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-dark-600 bg-dark-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="مثال: ریاضی و فیزیک"
              required
            />
          </div>

          <div>
            <label htmlFor="positiveCoefficient" className="block text-sm font-medium text-dark-200 mb-2">
              ضریب مثبت
            </label>
            <input
              type="number"
              id="positiveCoefficient"
              value={formData.positiveCoefficient}
              onChange={(e) => setFormData({ ...formData, positiveCoefficient: parseFloat(e.target.value) })}
              className="w-full px-3 py-2 border border-dark-600 bg-dark-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="مثال: 1"
              step="0.01"
              min="0"
              required
            />
          </div>

          <div className="flex justify-end space-x-4 space-x-reverse">
            <button
              type="button"
              onClick={() => navigate('/admin/course-groups')}
              className="bg-dark-600 hover:bg-dark-500 text-white px-6 py-2 rounded-md"
            >
              انصراف
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-md"
            >
              {isLoading ? 'در حال ذخیره...' : (isEdit ? 'ویرایش' : 'ایجاد')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CourseGroupForm;
