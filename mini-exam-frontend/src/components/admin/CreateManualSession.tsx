import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { User, Exam, UserRole } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const CreateManualSession: React.FC = () => {
  const navigate = useNavigate();
  
  const [students, setStudents] = useState<User[]>([]);
  const [exams, setExams] = useState<Exam[]>([]);
  const [selectedStudentId, setSelectedStudentId] = useState<number | null>(null);
  const [selectedExamId, setSelectedExamId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const [usersData, examsData] = await Promise.all([
          apiService.getUsers(),
          apiService.getExams(),
        ]);

        // Filter only students (users with USER role)
        const studentsOnly = usersData.filter(user => user.role === UserRole.USER);
        setStudents(studentsOnly);
        setExams(examsData);
      } catch (err: any) {
        setError('خطا در بارگذاری اطلاعات');
        console.error('Error fetching data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleProceed = () => {
    if (!selectedStudentId || !selectedExamId) {
      setError('لطفاً دانش‌آموز و آزمون را انتخاب کنید');
      return;
    }

    // Navigate to the exam session page with selected student and exam
    navigate(`/admin/exam-session/${selectedExamId}/student/${selectedStudentId}`);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">ایجاد جلسه آزمون دستی</h1>
          <button
            onClick={() => navigate('/admin/sessions')}
            className="bg-dark-600 hover:bg-dark-500 text-white px-4 py-2 rounded transition-colors"
          >
            بازگشت
          </button>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Student Selection */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-white">انتخاب دانش‌آموز</h2>
            
            {students.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-dark-300">هیچ دانش‌آموزی موجود نیست</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {students.map((student) => (
                  <div
                    key={student.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                      selectedStudentId === student.id
                        ? 'bg-primary-600/20 border-primary-500'
                        : 'bg-dark-700 border-dark-600 hover:bg-dark-600'
                    }`}
                    onClick={() => setSelectedStudentId(student.id)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-white font-medium">
                          {student.name} {student.lastName}
                        </h3>
                        <p className="text-dark-300 text-sm">
                          نام کاربری: {student.username}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name="student"
                          checked={selectedStudentId === student.id}
                          onChange={() => setSelectedStudentId(student.id)}
                          className="text-primary-600 focus:ring-primary-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Exam Selection */}
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-white">انتخاب آزمون</h2>
            
            {exams.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-dark-300">هیچ آزمونی موجود نیست</p>
              </div>
            ) : (
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {exams.map((exam) => (
                  <div
                    key={exam.id}
                    className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                      selectedExamId === exam.id
                        ? 'bg-primary-600/20 border-primary-500'
                        : 'bg-dark-700 border-dark-600 hover:bg-dark-600'
                    }`}
                    onClick={() => setSelectedExamId(exam.id)}
                  >
                    <div className="flex justify-between items-center">
                      <div>
                        <h3 className="text-white font-medium">{exam.name}</h3>
                        <p className="text-dark-300 text-sm">
                          تعداد دروس: {convertDigitsToPersian(exam.courses.length)}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <input
                          type="radio"
                          name="exam"
                          checked={selectedExamId === exam.id}
                          onChange={() => setSelectedExamId(exam.id)}
                          className="text-primary-600 focus:ring-primary-500"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="mt-8 flex justify-center">
          <button
            onClick={handleProceed}
            disabled={!selectedStudentId || !selectedExamId}
            className="bg-primary-600 hover:bg-primary-700 disabled:bg-primary-800 disabled:cursor-not-allowed text-white font-bold py-3 px-8 rounded-lg transition-colors"
          >
            ادامه و شروع آزمون
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateManualSession;
