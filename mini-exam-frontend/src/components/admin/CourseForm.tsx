import React, { useState, useEffect } from 'react';
import { useNavigate, useParams, Link } from 'react-router-dom';
import { CreateCourseDto, CourseGroup, Course } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

interface CourseFormProps {
  isEdit?: boolean;
}

const CourseForm: React.FC<CourseFormProps> = ({ isEdit = false }) => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  
  const [formData, setFormData] = useState<CreateCourseDto>({
    name: '',
    courseGroupId: 0,
    positiveCoefficient: 3,
    negativeCoefficient: 1,
  });
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string>('');

  const fetchData = async () => {
    try {
      const courseGroupsData = await apiService.getCourseGroups();
      setCourseGroups(courseGroupsData);

      if (isEdit && id) {
        const course = await apiService.getCourse(parseInt(id));
        const newFormData = {
          name: course.name,
          courseGroupId: course.courseGroupId,
          positiveCoefficient: course.positiveCoefficient,
          negativeCoefficient: course.negativeCoefficient,
        };
        setFormData(newFormData);
      } else if (courseGroupsData.length > 0) {
        // Only set default courseGroupId if it's not already set
        setFormData(prev => prev.courseGroupId === 0 ? { ...prev, courseGroupId: courseGroupsData[0].id } : prev);
      }
    } catch (err: any) {
      setError('خطا در بارگذاری اطلاعات');
      console.error('Error fetching data:', err);
    }
  };

  useEffect(() => {
    fetchData();
  }, [isEdit, id]);

  const refreshCourseGroups = async () => {
    setIsRefreshing(true);
    try {
      const oldGroupsCount = courseGroups.length;
      const courseGroupsData = await apiService.getCourseGroups();
      setCourseGroups(courseGroupsData);

      // اگر گروه جدیدی اضافه شده و هیچ گروهی انتخاب نشده، آخرین گروه را انتخاب کن
      if (courseGroupsData.length > oldGroupsCount && formData.courseGroupId === 0) {
        const newestGroup = courseGroupsData[courseGroupsData.length - 1];
        setFormData(prev => ({ ...prev, courseGroupId: newestGroup.id }));
      }

      setError(''); // پاک کردن خطاهای قبلی
    } catch (err: any) {
      setError('خطا در بارگذاری گروه‌های درسی');
      console.error('Error refreshing course groups:', err);
    } finally {
      setIsRefreshing(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      setError('نام درس الزامی است');
      return;
    }

    if (formData.courseGroupId === 0) {
      setError('انتخاب گروه درسی الزامی است');
      return;
    }

    if (formData.positiveCoefficient < 1 || formData.positiveCoefficient > 99) {
      setError('ضریب مثبت باید بین ۱ تا ۹۹ باشد');
      return;
    }

    if (formData.negativeCoefficient < 1 || formData.negativeCoefficient > 99) {
      setError('ضریب منفی باید بین ۱ تا ۹۹ باشد');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      if (isEdit && id) {
        await apiService.updateCourse(parseInt(id), formData);
      } else {
        await apiService.createCourse(formData);
      }

      navigate('/admin/courses');
    } catch (err: any) {
      setError(err.response?.data?.message || 'خطا در ذخیره درس');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'courseGroupId' || name === 'positiveCoefficient' || name === 'negativeCoefficient'
        ? parseInt(value) || 0
        : value,
    }));
  };

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">
            {isEdit ? 'ویرایش درس' : 'ایجاد درس جدید'}
          </h1>
          <button
            onClick={() => navigate('/admin/courses')}
            className="bg-dark-700 hover:bg-dark-600 text-white px-4 py-2 rounded-md"
          >
            بازگشت
          </button>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-dark-200 mb-2">
              نام درس
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-dark-600 bg-dark-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              placeholder="مثال: ریاضی"
              required
            />
          </div>

          <div>
            <div className="flex justify-between items-center mb-2">
              <label htmlFor="courseGroupId" className="block text-sm font-medium text-dark-200">
                گروه درسی
              </label>
              <button
                type="button"
                onClick={refreshCourseGroups}
                disabled={isRefreshing}
                className="text-xs bg-dark-600 hover:bg-dark-500 disabled:opacity-50 text-white px-2 py-1 rounded"
              >
                {isRefreshing ? 'در حال بارگذاری...' : '🔄 تازه‌سازی'}
              </button>
            </div>
            <select
              id="courseGroupId"
              name="courseGroupId"
              value={formData.courseGroupId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-dark-600 bg-dark-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              required
            >
              <option value={0}>انتخاب کنید</option>
              {courseGroups.map(group => (
                <option key={group.id} value={group.id}>
                  {group.name}
                </option>
              ))}
            </select>
            <div className="flex justify-between items-center mt-1">
              <p className="text-xs text-dark-400">
                اگر گروه درسی جدیدی ایجاد کرده‌اید، روی دکمه تازه‌سازی کلیک کنید
              </p>
              <Link
                to="/admin/course-groups/new"
                target="_blank"
                className="text-xs text-primary-400 hover:text-primary-300 underline"
              >
                + ایجاد گروه جدید
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="positiveCoefficient" className="block text-sm font-medium text-dark-200 mb-2">
                ضریب مثبت (۱-۹۹)
              </label>
              <input
                type="number"
                id="positiveCoefficient"
                name="positiveCoefficient"
                min="1"
                max="99"
                value={formData.positiveCoefficient}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-dark-600 bg-dark-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>

            <div>
              <label htmlFor="negativeCoefficient" className="block text-sm font-medium text-dark-200 mb-2">
                ضریب منفی (۱-۹۹)
              </label>
              <input
                type="number"
                id="negativeCoefficient"
                name="negativeCoefficient"
                min="1"
                max="99"
                value={formData.negativeCoefficient}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-dark-600 bg-dark-700 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                required
              />
            </div>
          </div>

          <div className="bg-dark-700 p-4 rounded-md">
            <h3 className="text-sm font-medium text-dark-200 mb-2">راهنما:</h3>
            <ul className="text-xs text-dark-300 space-y-1">
              <li>• ضریب مثبت: امتیاز هر پاسخ درست ({convertDigitsToPersian(1)} تا {convertDigitsToPersian(99)})</li>
              <li>• ضریب منفی: کسر امتیاز هر پاسخ غلط ({convertDigitsToPersian(1)} تا {convertDigitsToPersian(99)})</li>
              <li>• مثال: ضریب مثبت {convertDigitsToPersian(3)}، ضریب منفی {convertDigitsToPersian(1)} = هر پاسخ درست {convertDigitsToPersian(3)} امتیاز، هر پاسخ غلط {convertDigitsToPersian(1)} امتیاز کم می‌کند</li>
            </ul>
          </div>

          <div className="flex justify-end space-x-4 space-x-reverse">
            <button
              type="button"
              onClick={() => navigate('/admin/courses')}
              className="bg-dark-600 hover:bg-dark-500 text-white px-6 py-2 rounded-md"
            >
              انصراف
            </button>
            <button
              type="submit"
              disabled={isLoading}
              className="bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-6 py-2 rounded-md"
            >
              {isLoading ? 'در حال ذخیره...' : (isEdit ? 'ویرایش' : 'ایجاد')}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CourseForm;
