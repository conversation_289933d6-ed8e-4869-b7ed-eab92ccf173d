import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { CourseGroup } from '../../types';
import apiService from '../../services/api';
import { convertDigitsToPersian } from '../../utils/persianUtils';

const CourseGroupList: React.FC = () => {
  const [courseGroups, setCourseGroups] = useState<CourseGroup[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    const fetchCourseGroups = async () => {
      try {
        setIsLoading(true);
        const data = await apiService.getCourseGroups();
        setCourseGroups(data);
      } catch (err: any) {
        setError('خطا در بارگذاری گروه‌های درسی');
        console.error('Error fetching course groups:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCourseGroups();
  }, []);

  const handleDelete = async (id: number) => {
    if (!window.confirm('آیا از حذف این گروه درسی اطمینان دارید؟')) {
      return;
    }

    try {
      await apiService.deleteCourseGroup(id);
      setCourseGroups(prev => prev.filter(cg => cg.id !== id));
    } catch (err: any) {
      setError('خطا در حذف گروه درسی');
      console.error('Error deleting course group:', err);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"></div>
          <p className="mt-4 text-dark-300">در حال بارگذاری...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto">
      <div className="bg-dark-800 rounded-lg p-6">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold text-white">مدیریت گروه‌های درسی</h1>
          <Link
            to="/admin/course-groups/new"
            className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md"
          >
            ایجاد گروه جدید
          </Link>
        </div>

        {error && (
          <div className="bg-red-900/50 border border-red-500 text-red-200 px-4 py-3 rounded mb-6">
            {error}
          </div>
        )}

        {courseGroups.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-dark-300">هیچ گروه درسی موجود نیست</p>
            <Link
              to="/admin/course-groups/new"
              className="mt-4 inline-block bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-md"
            >
              ایجاد اولین گروه درسی
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-dark-700">
                <tr>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">شناسه</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">نام گروه</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">ضریب مثبت</th>
                  <th className="px-6 py-4 text-right text-sm font-medium text-dark-200">تاریخ ایجاد</th>
                  <th className="px-6 py-4 text-center text-sm font-medium text-dark-200">عملیات</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-dark-700">
                {courseGroups.map((courseGroup) => (
                  <tr key={courseGroup.id} className="hover:bg-dark-700/50">
                    <td className="px-6 py-4 text-dark-300">{convertDigitsToPersian(courseGroup.id)}</td>
                    <td className="px-6 py-4 text-white font-medium">{courseGroup.name}</td>
                    <td className="px-6 py-4 text-dark-300">{convertDigitsToPersian(courseGroup.positiveCoefficient)}</td>
                    <td className="px-6 py-4 text-dark-300">
                      {convertDigitsToPersian(new Date(courseGroup.createdAt).toLocaleDateString('fa-IR'))}
                    </td>
                    <td className="px-6 py-4 text-center">
                      <div className="flex justify-center space-x-2 space-x-reverse">
                        <Link
                          to={`/admin/course-groups/edit/${courseGroup.id}`}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
                        >
                          ویرایش
                        </Link>
                        <button
                          onClick={() => handleDelete(courseGroup.id)}
                          className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm"
                        >
                          حذف
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default CourseGroupList;
