import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate, Link } from 'react-router-dom';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/ProtectedRoute';
import LoginForm from './components/auth/LoginForm';
import RegisterForm from './components/auth/RegisterForm';
import ExamSession from './components/student/ExamSession';
import ExamList from './components/student/ExamList';
import SessionResult from './components/student/SessionResult';
import MySessionsList from './components/student/MySessionsList';
import ExamForm from './components/admin/ExamForm';
import ExamListAdmin from './components/admin/ExamList';
import CourseGroupList from './components/admin/CourseGroupList';
import CourseGroupForm from './components/admin/CourseGroupForm';
import CourseList from './components/admin/CourseList';
import CourseForm from './components/admin/CourseForm';
import SessionsList from './components/admin/SessionsList';
import CreateManualSession from './components/admin/CreateManualSession';
import UserList from './components/admin/UserList';
import UserForm from './components/admin/UserForm';
import StudentList from './components/admin/StudentList';
import StudentForm from './components/admin/StudentForm';
import AdminExamSession from './components/admin/AdminExamSession';
import AdminSessionResult from './components/admin/AdminSessionResult';
import { UserRole } from './types';

// Dashboard components (placeholder)
const Dashboard: React.FC = () => {
  const { user, isAdmin } = useAuth();

  return (
    <div className="text-center py-12">
      <h1 className="text-3xl font-bold text-white mb-4">
        خوش آمدید، {user?.username}
      </h1>
      <p className="text-dark-300 mb-8">
        {isAdmin() ? 'پنل مدیریت سیستم آزمون' : 'سیستم آزمون آنلاین'}
      </p>

      {isAdmin() ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 max-w-7xl mx-auto">
          <div className="bg-dark-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">گروه‌های درسی</h3>
            <p className="text-dark-300 text-sm mb-4">مدیریت گروه‌های درسی</p>
            <Link to="/admin/course-groups" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded inline-block">
              مشاهده
            </Link>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">دروس</h3>
            <p className="text-dark-300 text-sm mb-4">مدیریت دروس و ضرایب</p>
            <Link to="/admin/courses" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded inline-block">
              مشاهده
            </Link>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">آزمون‌ها</h3>
            <p className="text-dark-300 text-sm mb-4">ایجاد و مدیریت آزمون‌ها</p>
            <Link to="/admin/exams" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded inline-block">
              مشاهده
            </Link>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">جلسات آزمون</h3>
            <p className="text-dark-300 text-sm mb-4">مدیریت جلسات و محاسبه مجدد نمرات</p>
            <Link to="/admin/sessions" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded inline-block">
              مشاهده
            </Link>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">دانش‌آموزان</h3>
            <p className="text-dark-300 text-sm mb-4">مدیریت دانش‌آموزان و ثبت نتایج</p>
            <Link to="/admin/students" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded inline-block">
              مشاهده
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-2xl mx-auto">
          <div className="bg-dark-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">آزمون‌ها</h3>
            <p className="text-dark-300 text-sm mb-4">مشاهده و شرکت در آزمون‌ها</p>
            <Link to="/exams" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded inline-block">
              مشاهده
            </Link>
          </div>
          <div className="bg-dark-800 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-2">نتایج من</h3>
            <p className="text-dark-300 text-sm mb-4">مشاهده نتایج آزمون‌های قبلی</p>
            <Link to="/my-sessions" className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded inline-block">
              مشاهده
            </Link>
          </div>
        </div>
      )}
    </div>
  );
};

const Unauthorized: React.FC = () => (
  <div className="text-center py-12">
    <h1 className="text-2xl font-bold text-red-400 mb-4">دسترسی غیرمجاز</h1>
    <p className="text-dark-300">شما اجازه دسترسی به این صفحه را ندارید.</p>
  </div>
);

function App() {
  return (
    <AuthProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/login" element={<LoginForm />} />
            <Route path="/register" element={<RegisterForm />} />
            <Route path="/unauthorized" element={<Unauthorized />} />

            <Route path="/" element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            } />

            <Route path="/exams" element={
              <ProtectedRoute>
                <ExamList />
              </ProtectedRoute>
            } />

            <Route path="/exam/:examId" element={
              <ProtectedRoute>
                <ExamSession />
              </ProtectedRoute>
            } />

            <Route path="/session-result/:sessionId" element={
              <ProtectedRoute>
                <SessionResult />
              </ProtectedRoute>
            } />

            <Route path="/my-sessions" element={
              <ProtectedRoute>
                <MySessionsList />
              </ProtectedRoute>
            } />

            {/* Admin Routes */}
            <Route path="/admin/course-groups" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <CourseGroupList />
              </ProtectedRoute>
            } />

            <Route path="/admin/course-groups/new" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <CourseGroupForm />
              </ProtectedRoute>
            } />

            <Route path="/admin/course-groups/edit/:id" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <CourseGroupForm isEdit />
              </ProtectedRoute>
            } />

            <Route path="/admin/courses" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <CourseList />
              </ProtectedRoute>
            } />

            <Route path="/admin/courses/new" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <CourseForm />
              </ProtectedRoute>
            } />

            <Route path="/admin/courses/edit/:id" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <CourseForm isEdit />
              </ProtectedRoute>
            } />

            <Route path="/admin/exams" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <ExamListAdmin />
              </ProtectedRoute>
            } />

            <Route path="/admin/exams/new" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <ExamForm />
              </ProtectedRoute>
            } />

            <Route path="/admin/exams/edit/:id" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <ExamForm isEdit />
              </ProtectedRoute>
            } />

            <Route path="/admin/sessions" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <SessionsList />
              </ProtectedRoute>
            } />

            <Route path="/admin/sessions/create" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <CreateManualSession />
              </ProtectedRoute>
            } />

            <Route path="/admin/users" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <UserList />
              </ProtectedRoute>
            } />

            <Route path="/admin/users/new" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <UserForm />
              </ProtectedRoute>
            } />

            <Route path="/admin/users/edit/:id" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <UserForm isEdit />
              </ProtectedRoute>
            } />

            <Route path="/admin/students" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <StudentList />
              </ProtectedRoute>
            } />

            <Route path="/admin/students/new" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <StudentForm />
              </ProtectedRoute>
            } />

            <Route path="/admin/students/edit/:id" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <StudentForm isEdit />
              </ProtectedRoute>
            } />

            <Route path="/admin/exam-session/:examId/student/:studentId" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <AdminExamSession />
              </ProtectedRoute>
            } />

            <Route path="/admin/session-result/:sessionId" element={
              <ProtectedRoute requiredRole={UserRole.ADMIN}>
                <AdminSessionResult />
              </ProtectedRoute>
            } />

            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Layout>
      </Router>
    </AuthProvider>
  );
}

export default App;
